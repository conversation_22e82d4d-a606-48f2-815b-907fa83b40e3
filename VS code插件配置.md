
# VS code 插件配置

Paste JSON as Code  

drawio             画图
Excalidraw     手绘版 画图  

Markdown All in One
Markdown Footnotes
Markdown Preview Enhanced
Markdown Preview Github Styling
Markdown Preview Mermaid Support
Markdown+Math
Markmap
Marp for VS Code

SFTP 远程代码和本地同步     需要配置
[vscode SFTP](https://blog.csdn.net/qq_41685627/article/details/139066082)

连接K8s 集群pod
kubernetes ;   dev container ; remote development
找到pod 右击 attach vscode

GitLens 代码提交者查看

picGo   图床 + 阿里云对象存储
![image](https://fourt-wyq.oss-cn-shanghai.aliyun.com/images/image.png)
![image-1](https://fourt-wyq.oss-cn-shanghai.aliyun.com/images/image-1.png)

VScode如何Debug(调试)进入标准库文件 第三方包源码
![image-2](https://fourt-wyq.oss-cn-shanghai.aliyun.com/images/image-2.png)
在launch.json文件中配置 "justMyCode": false。这个选项默认是true，是进不了第三方包源码的，所以要改成false。

vscode把CRLF统一更换成LF
在vscode 设置中查找eol  ，选择对应的CRLF 或者是LF
