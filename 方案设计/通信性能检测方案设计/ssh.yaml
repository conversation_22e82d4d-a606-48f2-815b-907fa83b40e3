apiVersion: v1
data:
  hostfile: |
    trainingjob-m123456-m1-0 
    trainingjob-m123456-m2-0 
kind: ConfigMap
metadata:
  name: trainingjob-m123456-colossalai-hostfile-configmap
---

apiVersion: batch.volcano.sh/v1alpha1
kind: Job
metadata:
  name: trainingjob-m123456
  namespace: default
spec:
  maxRetry: 3
  minAvailable: 2
  plugins:
    env: []
    ssh: []
    svc: []
  policies:
  - action: CompleteJob
    event: TaskCompleted
  - action: TerminateJob
    event: TaskFailed
  queue: default
  schedulerName: volcano
  tasks:
  - maxRetry: 3
    minAvailable: 1
    name: m1
    replicas: 1
    template:
      metadata:
        annotations:
          k8s.v1.cni.cncf.io/networks: kube-system/macvlan-conf1
      spec:
        nodeName: a100-gpu-1
        containers:
        - command:
          - sh
          - -c
          - |
            mkdir -p /var/run/sshd; /usr/sbin/sshd -D;
          env:
          - name: CURRENT_VC_TASK_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.labels['volcano.sh/task-spec']
          - name: VC_TASK_NAMES
            valueFrom:
              fieldRef:
                fieldPath: metadata.annotations['volcano.sh/task-topology-task-order']
          - name: POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          - name: TZ
            value: Asia/Shanghai
          image:  xj-registry.bitahub.com:5000/autotest0304/nvidia-pytorch:24.01-py3-rdma-nccltest
          name: main
          ports:
          - containerPort: 22
            name: ssh-port
            protocol: TCP
          resources:
            limits:
              nvidia.com/nvidia-a100-sxm4-80GB: 2
              rdma/hca: "1"
            requests:
              nvidia.com/nvidia-a100-sxm4-80GB: 2
              rdma/hca: "1"
          volumeMounts:
          - mountPath: /etc/timezone
            name: mount-timezone
            readOnly: true
          - mountPath: /etc/localtime
            name: mount-localtime
            readOnly: true
          - mountPath: /usr/share/zoneinfo/Asia/Shanghai
            name: mount-localtime
            readOnly: true
          - mountPath: /dev/shm
            name: mount-share-memory
          - mountPath: /etc/colossalai
            name: hostfile
        nodeSelector:
          node-role.kubernetes.io/training: "true"
        restartPolicy: Never
        shareProcessNamespace: true
        volumes:
        - emptyDir:
            medium: Memory
            sizeLimit: "0"
          name: mount-share-memory
        - hostPath:
            path: /etc/localtime
          name: mount-localtime
        - hostPath:
            path: /etc/timezone
          name: mount-timezone
        - configMap:
            name: trainingjob-m123456-colossalai-hostfile-configmap
          name: hostfile
  - maxRetry: 3
    minAvailable: 1
    name: m2
    replicas: 1
    template:
      metadata:
        annotations:
          k8s.v1.cni.cncf.io/networks: kube-system/macvlan-conf1
      spec:
        nodeName: a100-gpu-2
        containers:
        - command:
          - sh
          - -c
          -  |
            mkdir -p /var/run/sshd; /usr/sbin/sshd -D;
          env:
          - name: CURRENT_VC_TASK_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.labels['volcano.sh/task-spec']
          - name: VC_TASK_NAMES
            valueFrom:
              fieldRef:
                fieldPath: metadata.annotations['volcano.sh/task-topology-task-order']
          - name: POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          - name: TZ
            value: Asia/Shanghai
          image:  xj-registry.bitahub.com:5000/autotest0304/nvidia-pytorch:24.01-py3-rdma-nccltest
          name: main
          ports:
          - containerPort: 22
            name: ssh-port
            protocol: TCP
          resources:
            limits:
              nvidia.com/nvidia-a100-sxm4-80GB: 2
              rdma/hca: "1"
            requests:
              nvidia.com/nvidia-a100-sxm4-80GB: 2
              rdma/hca: "1"
          volumeMounts:
          - mountPath: /etc/timezone
            name: mount-timezone
            readOnly: true
          - mountPath: /etc/localtime
            name: mount-localtime
            readOnly: true
          - mountPath: /usr/share/zoneinfo/Asia/Shanghai
            name: mount-localtime
            readOnly: true
          - mountPath: /dev/shm
            name: mount-share-memory
          - mountPath: /etc/colossalai
            name: hostfile
        nodeSelector:
          node-role.kubernetes.io/training: "true"
        restartPolicy: Never
        shareProcessNamespace: true
        volumes:
        - emptyDir:
            medium: Memory
            sizeLimit: "0"
          name: mount-share-memory
        - hostPath:
            path: /etc/localtime
          name: mount-localtime
        - hostPath:
            path: /etc/timezone
          name: mount-timezone
        - configMap:
            name: trainingjob-m123456-colossalai-hostfile-configmap
          name: hostfile