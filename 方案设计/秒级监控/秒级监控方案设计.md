# 秒级监控方案

## 问题背景

在网络监控中，当前Prometheus采集数据最小的时间周期是15s,如果两次采集点数据正常，用户就会认为网络性能是正常的，实际上15s之间如果突发大幅的网络波动是无法感知的。采集频率1s和15s的网络指标对比如下图所示：

![second-6-2025-04-16](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/second-6-2025-04-16.png)

方案目标如下：

1. 在完成网络秒级监控的情况下，保证Prometheus的存储性能和稳定性。
2. 在产品上如何展示秒级监控数据，让用户直观的感受到网络指标数据的变化。

## 方案验证

### 竞品分析

`去哪儿旅行`秒级监控预警方案结构图如下：
![second-5-2025-04-16](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/second-5-2025-04-16.png)

[阿里云秒级监控功能详述](https://help.aliyun.com/zh/slb/network-load-balancer/user-guide/nlb-access-high-precision-second-monitoring-to-be-modified)

[华为云秒级监控功能详述](https://support.huaweicloud.com/intl/zh-cn/usermanual-dds/dds_03_0090.html)

[火山云秒级监控功能详述](https://www.volcengine.com/docs/6419/1131755)

### 技术选型

#### pushgateway

**实现思路：** 使用自定义的exporter，将网络监控指标按照规定的采集频率推送到pushgateway，然后由Prometheus从pushgateway中定期拉取数据。

方案结构图如下：

![second-4-2025-04-16](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/second-4-2025-04-16.png)

**结论：在使用pushgateway的时候，自定义的exporter无法上传携带时间戳的数据。无法实现秒级监控的功能。**

![second-2025-04-16](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/second-2025-04-16.png)

#### 秒级监控Prometheus

**实现思路：** 通过新增一个Prometheus来保存秒级监控的监控指标，默认只收集hero-user命名空间的监控指标。并只保存较短的时间周期数据（例如一天）。

方案结构图如下：

![second-3-2025-04-16](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/second-3-2025-04-16.png)
**功能验证：** 调整kubelet(cadvisor)的配置，将默认的30s采集频率调整为1s。对应的servicemonitor配置修改如下：

```bash
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    app.kubernetes.io/name: kubelet
    app.kubernetes.io/part-of: kube-prometheus
  name: kubelet
  namespace: monitoring
spec:
  endpoints:
  - bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
    honorLabels: true
    honorTimestamps: false
    interval: 1s
    metricRelabelings:
    - action: drop
      regex: container_(network_tcp_usage_total|network_udp_usage_total|tasks_state|cpu_load_average_10s)
      sourceLabels:
      - __name__
    - action: drop
      regex: (container_spec_.*|container_file_descriptors|container_sockets|container_threads_max|container_threads|container_start_time_seconds|container_last_seen);;
      sourceLabels:
      - __name__
      - pod
      - namespace
    - action: drop
      regex: (container_blkio_device_usage_total);.+
      sourceLabels:
      - __name__
      - container
    path: /metrics/cadvisor
    port: https-metrics
    relabelings:
    - action: replace
      sourceLabels:
      - __metrics_path__
      targetLabel: metrics_path
    scheme: https
    tlsConfig:
      insecureSkipVerify: true
  jobLabel: app.kubernetes.io/name
  namespaceSelector:
    matchNames:
    - kube-system
  selector:
    matchLabels:
      app.kubernetes.io/name: kubelet 
```

验证结果：
![second-1-2025-04-16](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/second-1-2025-04-16.png)

> Prometheus配置文件更新
> `curl -X POST http://************:9090/-/reload`

**结论：实现简单，可以完成秒级监控的需求；在仅配置用户监控指标所需的指标后，Prometheus的存储压力和性能压力显著降低（0.7c/200M）；增加了exporter的负载。**

> 注： 单纯的提高网络监控的exporter的采集频率，对Prometheus本身增加了很大的存储压力和性能压力，并且存储传输item次数的限制。
> 当监控指标的发送速率超过10000 item/s，导致Prometheus接受数据失败。报错如下所示：

```bash
time=2025-03-21T09:10:26.836Z level=ERROR source=queue_manager.go:1670 msg="non-recoverable error" component=remote remote_name=d09508 url=http://mimir-nginx.mimir.svc:80/api/v1/push failedSampleCount=2000 failedHistogramCount=0 failedExemplarCount=0 err="server returned HTTP status 429 Too Many Requests: the request has been rejected because the tenant exceeded the ingestion rate limit, set to 10000 items/s with a maximum allowed burst of 200000. This limit is applied on the total number of samples, exemplars and metadata received across all distributors (err-mimir-tenant-max-ingestion-rate). To adjust the related per-tenant limits, configure -distributor.ingestion-rate-limit and -distributor.ingestion-burst-size, or contact your service administrator.\n
```

#### 自定义exporter + 秒级监控Prometheus

**实现思路：** 构建秒级监控exporter服务：通过将node-exporter中采集网络相关的监控指标的逻辑抽取出来，并通过秒级定时任务去采集数据和时间戳到缓存中，然后通过http接口暴露数据，供Prometheus采集定时采集。

整体结构图如下所示：

![second-8-2025-04-16](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/second-8-2025-04-16.png)

可以根据后期需求来动态调整不同类指标的采集间隔，比如网络指标可以调整为1s，cpu指标可以调整为10s，磁盘指标可以调整为10s等。

代码仓库：<https://gitlab.bitahub.com/hero-os/hero-exporter/-/tree/dev?ref_type=heads>

#### 结论

可以实现秒级监控，并可实现后期业务需求的增加。但每次业务需求都需要定制开发。

## 解决方案

秒级监控功能的应用场景：在多机分布式训练和多机推理的过程中，通过秒级监控实现IB网络问题定位，模型性能优化。

针对秒级监控能力对Prometheus的存储和io的影响较大。所以在任务级别开启网络秒级监控，能有效减少无用数据的产生并减少存储io压力。

方案结构图如下：

![image-2025-04-24](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/image-2025-04-24.png)

秒级监控任务下发流程如下:

1. 用户提交秒级监控任务：用户创建带秒级监控标签（如monitor: high-frequency）的 Pod 并提交至 Kubernetes。
2. 调度器分配节点：Kubernetes 调度器将 Pod 调度到目标节点，节点创建 Pod 实例。
3. Exporter检测任务：节点上的Exporter 定期监控本地 Pod 列表，识别是否存在带秒级监控标签的 Pod。
4. 动态调整频率：若检测到此类 Pod，Exporter 将指标拉取间隔强制设为 1 秒；若不存在，则保持默认频率（如 15 秒）。

```mermaid
sequenceDiagram
    participant 用户
    participant 调度器
    participant 节点
    participant 任务Pod
    participant Exporter
    
    用户->>K8s API: 提交带标签的Pod（标签: monitor=high-frequency）
    K8s API->>调度器: 触发Pod调度
    调度器->>节点: 分配Pod到目标节点
    节点->>节点: 创建Pod实例
    任务Pod-->>节点: 进入运行状态
    
    Exporter->>节点: 轮询Pod列表（定时检查）
    节点-->>Exporter: 返回Pod列表（含目标标签Pod）
    Exporter->>Exporter: 检测到标签匹配（秒级监控任务）
    Exporter->>Exporter: 调整拉取间隔为1s
    
    任务Pod->>节点: 任务结束（Pod终止）
    节点->>节点: 删除Pod实例
    Exporter->>节点: 轮询Pod列表（定时检查）
    节点-->>Exporter: 返回Pod列表（无目标标签Pod）
    Exporter->>Exporter: 未检测到标签
    Exporter->>Exporter: 恢复默认拉取频率（如15s）
```

实施流程：

1. 系统测需要修改node-exporter源码，增加ib网络的秒级监控能力。
2. 产品和前端需要构建一个秒级监控面板，考虑用户交互。

### grafana秒级监控网络面板配置

```bash

tcp 连接状态

label_replace(kube_pod_info{pod="$pod"}, "instance","$1","node","(.*)") * on(instance) group_right sum (node_tcp_connection_states) by (state,instance)

丢包数量

label_replace(kube_pod_info{pod="$pod"}, "instance","$1","node","(.*)") * on(instance) group_right sum by  (device,instance) (irate(node_network_receive_drop_total{device!~"lo.*|cali.*|docker.*"}[$__rate_interval]) > 0 )

label_replace(kube_pod_info{pod="$pod"}, "instance","$1","node","(.*)") * on(instance) group_right sum by  (device,instance) (irate(node_network_transmit_drop_total{device!~"lo.*|cali.*|docker.*"}[$__rate_interval]) > 0 )

backlog 数量

label_replace(kube_pod_info{pod="$pod"}, "instance","$1","node","(.*)") * on(instance) group_right sum (node_softnet_backlog_len) by (instance)


label_replace(kube_pod_info{namespace="hero-user",pod="$pod"}, "instance","$1","node","(.*)") * on(instance) group_right rate(node_network_receive_drop_total{job="node-exporter", device!~"lo.*|cali.*|docker.*|tunl.*"}[$__rate_interval]) 

label_replace(kube_pod_info{namespace="hero-user",pod="$pod"}, "instance","$1","node","(.*)") * on(instance) group_right rate(node_network_transmit_drop_total{job="node-exporter", device!~"lo.*|cali.*|docker.*|tunl.*"}[$__rate_interval])


label_replace(kube_pod_info{namespace="hero-user",pod="$pod"}, "instance","$1","node","(.*)") * on(instance) group_right rate(node_network_receive_bytes_total{job="node-exporter", device!~"lo.*|cali.*|docker.*|tunl.*"}[$__rate_interval]) * 8 > 0

label_replace(kube_pod_info{namespace="hero-user",pod="$pod"}, "instance","$1","node","(.*)") * on(instance) group_right rate(node_network_transmit_bytes_total{job="node-exporter", device!~"lo.*|cali.*|docker.*|tunl.*"}[$__rate_interval]) * 8  > 0

kube_pod_status_phase{namespace="hero-user",phase="Running"} > 0

```

验证

```bash
node_infiniband_physical_state_id{instance="system-test-100-17-gpu-3090"}[30s]

label_replace(kube_pod_info{namespace="hero-user",pod="a15075319334301696231135-imagemaker"}, "instance","$1","node","(.*)") * on(instance) group_right rate(node_infiniband_port_data_received_bytes_total[1m0s]) * 8   
```

展示30s中这个指标采集得点数。

## 波及分析

当前方案不影响任何业务。

## 最终结论

1. 不新增一个Prometheus来存储秒级监控数据。
2. 需要在任务级别上做秒级监控能力，尽量减少存储和ip的压力。
    可以考虑的实现方式：
    1. exporter 感知秒级监控的任务，调整上传频率。pod信息如何跟设备名称进行绑定。？
    2. promrtheus 存储的时候针对非秒级监控任务做下采样。 Prometheus本身不具备下采样的能力
    3. 使用中间服务统一采集所有exporter，按照任务不同拉取频率不通，然后Prometheus拉取中间服务。 如何watch所有exporter的指标接口变化？

## 参考文档

<https://mp.weixin.qq.com/s/evc-50dYZfA0MvZs_GTX6Q>
<https://mp.weixin.qq.com/s/dF5YS7EPp6U_L0pCyx9_gg>
<https://zhuanlan.zhihu.com/p/31777906>
<https://blog.csdn.net/a1369760658/article/details/147719279>
