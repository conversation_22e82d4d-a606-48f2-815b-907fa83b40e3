# 使用token 对kubelet 进行接口访问

```bash

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: temp-role
rules:
- apiGroups:
  - ""
  resources:
  - nodes/metrics
  - nodes/stats
  - nodes/proxy
  verbs:
  - get

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: temp-rolebinding
  namespace: default
subjects:
  - kind: ServiceAccount
    name: temp-sa
    namespace: default
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: temp-role

---

apiVersion: v1
kind: ServiceAccount
metadata:
  name: temp-sa
  namespace: default

---

apiVersion: v1
kind: Secret
metadata:
  name: temp-sa-secret
  namespace: default
  annotations:
    kubernetes.io/service-account.name: "temp-sa"
type: kubernetes.io/service-account-token
```

curl -s -k -H "Authorization: Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" <https://***********:10250/pods/fluid-system> | jq

结果正常访问
