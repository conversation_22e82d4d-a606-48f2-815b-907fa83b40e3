# 多云监控方案

## 问题背景

当前单集群监控部署监控方案架构如下：
![sig-1-2025-03-21](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/sig-1-2025-03-21.png)

当前多云集群监控部署方案架构如下：

![mutli-1-2025-03-21](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/mutli-1-2025-03-21.png)

监控方案涉及的自定义和开源组件如下：

- kube-state-metrics：提供K8S CR状态信息
- node-exporter: 提供节点的网络、存储、CPU、内存、磁盘等指标。
- dcgm-exporter：提供GPU相关指标。
- cadvisor(kubelet) ： 提供容器的网络、存储、CPU、内存、磁盘等指标。
- prometheus：监控数据存储、查询、告警。
- grafana：监控数据可视化;提供grafana相关监控指标。
- resource-manager：提供监控api接口，监控数据后处理。
- kubernetes-pods：pod 级别的自动发现，提供istio 推理的监控指标。
- minio：提供对象存储相关监控指标。
- alertmanager：提供告警相关指标。
- coredns：提供dns 相关指标。
- hero-exporter：提供gpu卡时相关指标。
- kube-apiserver: 提供apiserver相关指标。
- kube-controller-manager: 提供controller-manager相关指标。
- kube-scheduler: 提供scheduler相关指标。
- kube-proxy: 提供proxy相关指标。
- kube-etcd: 提供etcd相关指标。
- prometheus-operator: 提供prometheus-operator相关指标。

**当前监控方案中，存在如下问题：**

- **监控数据存储在每个子集群中，没有办法进行统一存储，统一查询，无法满足多集群的数据聚合业务场景。**
- **多云场景中，子集群下线后，用户无法查看历史监控数据。**
- **单个Prometheus统一接受所有组件上报的监控数据，监控指标数据没有分流，数据存储压力大，查询性能也会受到影响。**

## 解决方案

### 减少存储压力，提升查询性能

首先针对如何减少单集群监控数据的存储压力和提高指标查询性能，我们可以从下面几点进行考虑：

#### 数据分流

因为在现有的监控方案的部署过程中，Prometheus存在很多跟用户无关的监控数据，例如数据库状态，服务的可用性等指标，这些指标数据对用户来说没有太大的意义，因此需要将用户相关的监控数据从其他无关的监控数据中分离出来，从而减轻Prometheus的存储压力，提高查询性能和减少存储压力。

![sig-2-2025-03-24](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/sig-2-2025-03-24.png)

**实施流程：**

- 根据job来进行数据分流，user-prometheus配置当前用户端使用的监控指标数据来源，具体exporter如下：
  - node-exporter
  - kube-state-metrics
  - kubelet(cadvisor)
  - dcgm-exporter
  - npu-exporter
  - kubernetes-pods
- system-Prometheus配置剩余exporter，提供短期的数据查询。
- resource-manager只对接user-prometheus。
- grafana对接user-prometheus和system-prometheus。

#### Prometheus升级

相比较Prometheus 2.41在prometheus 3.2.0版本中，主要的优化点是对`内存`的使用优化。官方描述2.47 版本后prometheus的内存消耗减少了一半[30 Pull Requests Later, Prometheus Memory Use Is Cut in Half - The New Stack](https://thenewstack.io/30-pull-requests-later-prometheus-memory-use-is-cut-in-half/)

参考github版本说明 [Releases · prometheus/prometheus](https://github.com/prometheus/prometheus/releases)

**实施流程：**

- 将Prometheus 2.41 版本升级到 Prometheus 3.2.0。

#### Exporter调整

kube-state-metrics部署时容器命令添加如下:

```bash
- args:
  - --host=127.0.0.1
  - --port=8081
  - --telemetry-host=127.0.0.1
  - --telemetry-port=8082
  - --resources=pods,nodes  # 设置只采集 nodes 和pods的状态信息
  - --metric-labels-allowlist=nodes=[*],pods=[*]
  image: registry.cnbita.com:5000/cluster-images/kube-state-metrics:v2.15.0
```

### 子集群监控数据统一存储到主控集群

针对**子集群下线后，用户无法查看历史监控数据**的问题，我们通过调研当前主流的多云监控方案，将**子集群的数据实时同步到主控集群**，用户始终查询主控的监控数据，从而解决上述提到的问题。
我们通过对比当前主流的开源项目中，根据比较**社区活跃度，部署难度，性能分析，和运维难度**等方面来进行对比，在多云主控下部署**VictoriaMetrics**方案最优，对比结果如下表：

| 维度       | VictoriaMetrics                                               | Thanos                                           | Cortex                              | Grafana Mimir                                       |
| ---------- | ------------------------------------------------------------- | ------------------------------------------------ | ----------------------------------- | --------------------------------------------------- |
| 性能       | 写入与查询性能最优，单节点支持百万级数据点/秒                 | 查询性能受对象存储延迟影响，高基数场景易出现瓶颈 | 类似 Thanos，依赖对象存储，性能中等 | 与 Thanos 架构类似，优化查询缓存，性能略优于 Thanos |
| 存储效率   | 压缩率最高（比 Prometheus 减少 70% 存储）                     | 依赖对象存储，压缩率一般                         | 同 Thanos，存储成本较高             | 类似 Thanos，但支持更高效的分块存储                 |
| 架构复杂度 | 组件少（vminsert/vmselect/vmstorage），部署简单               | 组件多（Sidecar/Store/Query 等），运维复杂       | 与 Thanos 类似，组件较多            | 类似 Thanos，但优化了组件交互逻辑                   |
| 扩展性     | 水平扩展灵活，存储与计算分离                                  | 依赖 Prometheus 实例扩展，对象存储扩展性受限     | 水平扩展能力中等                    | 支持动态分片，扩展性优于 Thanos                     |
| 成本       | 本地存储成本低，流量成本低                                    | 对象存储成本低，但跨区流量费用高                 | 同 Thanos                           | 存储成本类似 Thanos，企业版许可费用较高             |
| 可靠性     | 数据强一致性，宕机时数据丢失风险低（秒级）                    | 数据上传周期长（2小时），宕机可能丢失较多数据    | 同 Thanos                           | 优化了数据块合并策略，可靠性略高                    |
| 生态兼容性 | 完全兼容 Prometheus API，支持多数据源（InfluxDB/Graphite 等） | 仅支持 Prometheus 生态                           | 仅支持 Prometheus 生态              | 深度集成 Grafana，兼容 Prometheus                   |

方案结构图如下：

![multi-1-2025-04-08](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/multi-1-2025-04-08.png)

主控对接子集群的时候，是需要内网互通或者公网可访问的。所以可以通过Prometheus联邦的方式，通过主控集群的Prometheus来主动拉取子集群Prometheus中的数据，然后在将主控的Prometheus数据发送到VictoriaMetrics中进行数据存储和提供指标查询功能。这样在主控对接子集群的时候，只需要调整主控的配置即可。

**实施流程**：

1. 主控的prometheus CR 需要置remoteWrite参数并指向主控的victoriametrics的访问地址，具体配置如下：

  ```bash
  remoteWrite:
    - url: "http://<victoriametrics-ip>:<victoriametrics-port>/api/v1/write"
  ```

2. 通过下面配置servicemonitor,更新prometheus的配置，配置如下：

  ```bash
  apiVersion: v1
  kind: Endpoints
  metadata:
    name: prometheus-federation
    namespace: monitoring
    labels:
      app: prometheus-federation
  subsets:
  - addresses:
    - ip: ***********  # 公网或者内网地址
    ports:
    - name: http
      port: 41424
      protocol: TCP
  ---
  apiVersion: v1
  kind: Service
  metadata:
    name: prometheus-federation
    namespace: monitoring
    labels:
      app: prometheus-federation
  spec:
    ports:
    - name: http
      protocol: TCP
      port: 41424
      targetPort: 41424
    sessionAffinity: None
    type: ClusterIP
  ---
  apiVersion: monitoring.coreos.com/v1
  kind: ServiceMonitor
  metadata:
    name: prometheus-federation
    namespace: monitoring
  spec:
    selector:
      matchLabels:
        app: prometheus-federation
    namespaceSelector:
      matchNames:
        - monitoring
    endpoints:
      - port: http
        interval: 30s
        honorLabels: true
        path: /federate
        params:
          'match[]':
            - '{__name__=~".+", pod="tj-a15235988875898880631565-worker2-0"}'
  ```

3. Resource-Manager (系统测修改)
   1. 监控指标查询接口的方式需要从Prometheus sdk改成Http服务请求。
   2. 某些查询指标需要进行调整。
  
4. Grafana 修改项（系统测和前端修改）
   1. grafana 面板需要添加cluster参数，因子集群数据直接同步到主控集群，所以可以不通过数据源的方式从子集群Prometheus进行查询。前端接口需要进行调整。
   2. 某些查询指标需要进行调整。

**针对单集群对接多个主控的情况下，用户数据是否需要分流到不同的主控集群中？**

需要上传数据到主控中心的exporter如下:

- node-exporter
- kube-state-metrics
- kubelet(cadvisor)
- dcgm-exporter
- npu-exporter
- kubernetes-pods

解决方案：

根据pod的名称进行数据分流（主控下发的任务名称需要携带对应的clusterID）

```bash
'match[]':
  - '{__name__=~".+", pod=~".*clusterID.*"}'   
  - '{__name__=~".+", exported_pod=~".*clusterID.*"}' 
```

## 方案验证

### Prometheus升级验证

#### 使用现状

当前标准部署流程中prometheus的版本是2.41，在长期观察使用过程中，存在以下问题。

1. 在长期存储的场景下，数据压缩和长时间段查询会导致prometheus OOM 并重启 ，导致prometheus的数据存储存在间歇性无法采集到数据的情况。
2. 存在expoter能正常暴露数据，但是prometheus本身无法采集到数据的情况。
3. node-exporter 存在某个节点的数据无法上报的情况。
4. 算法环境存在近期（几天前）的数据无法查询的情况
5. 卡时需求在30天的查询时间段存在Prometheus直接oomkill问题
6. prometheus 配置了很多暂时用不到servicemonitor，采集的数据量很多

![prometheus-1-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/prometheus-1-2025-03-18.png)
![prometheus-2-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/prometheus-2-2025-03-18.png)
![prometheus-3-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/prometheus-3-2025-03-18.png)

#### Prometheus 3.2.0 版本更新验证

**接口请求时间对比验证**

| 接口名称                                                       | 名称            | 总请求数 | 失败数 | 失败率 | 平均响应时间(ms) | 最小响应(ms) | 最大响应(ms) | 90% (ms) | 95% (ms) | 99% (ms) | TPS  | 平均接收流量每秒请求数 | 每秒请求数 |
| -------------------------------------------------------------- | --------------- | -------- | ------ | ------ | ---------------- | ------------ | ------------ | -------- | -------- | -------- | ---- | ---------------------- | ---------- |
| GET <http://10.0.101.61:33493/api/v1/query?query=kube_node_info> | prometheus-2.41 | 633      | 0      | 0%     | 159.65           | 0            | 822          | 69       | 504      | 724      | 9.31 | 39.41                  | 8.64       |
| GET <http://***********:41424/api/v1/query?query=kube_node_info> | prometheus-3.2  | 599      | 0      | 0%     | 164.09           | 0            | 1157         | 280      | 506      | 846      | 9.2  | 33.33                  | 7.39       |

**CPU 和内存使用占比对比验证**
![prometheus-4-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/prometheus-4-2025-03-18.png)
![prometheus-5-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/prometheus-5-2025-03-18.png)

**数据存储占比验证**
开发环境 prometheus-3.2,保存数据一天

```bash
[root@yigou-dev-102-69 prometheus]# du -sh prometheus-db/
2.9G    prometheus-db/
[root@yigou-dev-102-69 prometheus]# cd prometheus-db/
[root@yigou-dev-102-69 prometheus-db]# ll
total 884220
drwxr-xr-x  7 <USER> <GROUP>  10088448 Mar 10 17:00 01JNZMGP1RB9NGKME3NND81ZVK.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  13611008 Mar 10 19:00 01JNZVCD9W8ZHVATSCWV6Z246A.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  23740416 Mar 10 21:00 01JP0283VZAWP6EAQEYMW9N0GR.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  23343104 Mar 10 23:00 01JP093V3KB66MF2WM35KA3Z48.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  23728128 Mar 11 01:00 01JP0FZJBWFVK909RX960C3EFB.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  23601152 Mar 11 03:00 01JP0PV9KGX1AK89AZ8JZEGF8V.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  23343104 Mar 11 05:00 01JP0XQ0VG9PTY12NAB6VZBHSS.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  23638016 Mar 11 07:00 01JP14JR407R27444EJ0PAX3WP.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  23396352 Mar 11 09:00 01JP1BEFC0ZKH5YDX5GXMPXCYR.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  23515136 Mar 11 11:00 01JP1JA6KN23K30FPV80Z4GS0F.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  24088576 Mar 11 13:00 01JP1S5XKJTJ3079C3PNHDC3X8.tmp-for-deletion
drwxr-xr-x  7 <USER> <GROUP>  23207936 Mar 11 15:00 01JP201N3J5S9PE3798B6DSZ51.tmp-for-deletion
drwxr-xr-x  7  <USER> <GROUP>  23678976 Mar 16 19:00 01JPF9RPMPVKWD68FBR59KV0PR
drwxr-xr-x  7  <USER> <GROUP>  24154112 Mar 16 21:00 01JPFGMDWMF25WYXKMXDQ1RX3P
drwxr-xr-x  7  <USER> <GROUP>  23736320 Mar 16 23:00 01JPFQG54KZ61FA1NHJAZ0W442
drwxr-xr-x  7  <USER> <GROUP>  23728128 Mar 17 01:00 01JPFYBWCKTZ2RMVV3T7YS59N2
drwxr-xr-x  7  <USER> <GROUP>  23650304 Mar 17 03:00 01JPG57KMNG99D5RVPWYS6EDEB
drwxr-xr-x  7  <USER> <GROUP>  23719936 Mar 17 05:00 01JPGC3AWKDBJ6HEZ6TP1T2T7N
drwxr-xr-x  7  <USER> <GROUP>  24133632 Mar 17 07:00 01JPGJZ24P1QY1J86XEQP0R226
drwxr-xr-x  7  <USER> <GROUP>  23789568 Mar 17 09:00 01JPGSTSCQ8MT56M469G1CB100
drwxr-xr-x  7  <USER> <GROUP>  24100864 Mar 17 11:00 01JPH0PGF60AGP03VH2MCAD82F
drwxr-xr-x  7  <USER> <GROUP>  23691264 Mar 17 13:00 01JPH7J835WPHE9D94WYMKTT48
drwxr-xr-x  7  <USER> <GROUP>  24358912 Mar 17 15:00 01JPHEDZ4QFPMQEGKS2BY47D8C
drwxr-xr-x  7  <USER> <GROUP>  24371200 Mar 17 17:00 01JPHN9PCTDWFX8TESDYXP7B8S
drwxrwxrwx  5  <USER> <GROUP>  25370624 Mar 17 17:18 chunks_head
-rw-r--r--  1  <USER> <GROUP>         0 Mar 17 17:05 lock
-rwxrwxrwx  1  <USER> <GROUP>     20001 Mar 17 17:49 queries.active
drwxrwxrwx 11  <USER> <GROUP> 333635584 Mar 17 17:05 wal
```

测试环境 prometheus 2.41 保存数据一天

```bash
[root@yigou-stg-101-69 prometheus-db]# ll
total 3112404
drwxr-xr-x  7 <USER> <GROUP> 201785344 Mar 16 17:00 01JPF2X1AK4HR2R0FGJ4WJ9H5R
drwxr-xr-x  7 <USER> <GROUP> 181075968 Mar 16 19:00 01JPF9RRHPV2DF0DWCRDZ5V9S2
drwxr-xr-x  7 <USER> <GROUP> 183742464 Mar 16 21:00 01JPFGMFS3RP8BFK4XGNQREH4J
drwxr-xr-x  7 <USER> <GROUP> 180129792 Mar 16 23:00 01JPFQG7291835MQBGTS23GW5H
drwxr-xr-x  7 <USER> <GROUP> 172158976 Mar 17 01:00 01JPFYBY9FMX1BKRZN15V9WKRQ
drwxr-xr-x  7 <USER> <GROUP> 172908544 Mar 17 03:00 01JPG57NH0HAWB97E44P0F1RVH
drwxr-xr-x  7 <USER> <GROUP> 172462080 Mar 17 05:00 01JPGC3CWKEP8NJWZTNMXV9E7E
drwxr-xr-x  7 <USER> <GROUP> 170082304 Mar 17 07:00 01JPGJZ41GKKSBZQ0AJTVS2BGS
drwxr-xr-x  7 <USER> <GROUP> 170999808 Mar 17 09:00 01JPGSTVB06SGXBX5BQDQ5BVGJ
drwxr-xr-x  7 <USER> <GROUP> 172638208 Mar 17 11:00 01JPH0PJMFXJN2CTKRCJPHGHEG
drwxr-xr-x  7 <USER> <GROUP> 168775680 Mar 17 13:00 01JPH7J9VNXPXAW5YJA2MZKY59
drwxr-xr-x  7 <USER> <GROUP> 170446848 Mar 17 15:00 01JPHEE14772BRPFEKYJ93ZEKE
drwxr-xr-x  7 <USER> <GROUP> 173338624 Mar 17 17:00 01JPHN9R9MTRX0TWKAE574Y0V6
drwxr-xr-x  5 <USER> <GROUP> 165650432 Mar 17 17:00 chunks_head
-rw-r--r--  1 <USER> <GROUP>         0 Mar  6 15:21 lock
-rw-r--r--  1 <USER> <GROUP>     20001 Mar 17 17:53 queries.active
drwxr-xr-x 10 <USER> <GROUP> 730886144 Mar 17 17:27 wal
[root@yigou-stg-101-69 prometheus-db]# cd ..
[root@yigou-stg-101-69 monitoring-prometheus]# du -sh prometheus-db/
11G     prometheus-db/
```

#### 带数据升级Prometheus 3.2版本验证

复制后的数据需要添加权限，否则无法读取。

```bash
scp -r prometheus-db/*  ***********:/user-storage/gbasic-component/monitoring/prometheus/prometheus-db
cd /user-storage/gbasic-component/monitoring/prometheus/prometheus-db
chmod -R 777 *
```

数据从测试环境复制到开发环境，验证数据是否能正常读取?

验证结果如下：

![dataset-1-2025-03-21](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/dataset-1-2025-03-21.png)

算法环境的指标数据，在开发环境中是否正常读取？

验证结果如下：
![dataset-2-2025-03-21](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/dataset-2-2025-03-21.png)

### 统一存储方案验证

#### Grafana Mimir

##### Mimir 功能特性

1. `易维护性`：Grafana Mimir 的核心优势之一便是易于“安装和维护”。该项目的大量文档、教程和部署工具使其入门起来既快速又简单。Grafana Mimir 的整体模式允许只使用一个二进制文件，不需要额外的依赖项。此外，与 Grafana Mimir 一起打包的最佳实践仪表板、警报和运行手册可以轻松监控系统的健康状况并保持其平稳运行。
2. `可扩展性`：Grafana Mimir 的水平可扩展架构使其能够处理大量时间序列数据。内部测试表明，该系统可以处理多达 10 亿个活动时间序列，从而实现大规模的可扩展性。这意味着 Grafana Mimir 可以跨多台机器运行，从而能够处理比单个 Prometheus 实例多几个数量级的时间序列。
3. `全局视图`：Grafana Mimir 的另一个关键优势是它能够提供全局的指标视图。该系统使用户能够运行聚合来自多个 Prometheus 实例的系列的查询，从而提供所有系统的全面视图。查询引擎还广泛并行化查询执行，即使是最高基数的查询也能以极快的速度执行。
4. `数据持久性`：Grafana Mimir 使用对象存储来进行长期数据存储，利用了这种无处不在的、高性价比、高耐久性的技术。该系统与多个对象存储实现兼容，包括 AWS S3、谷歌云存储、Azure Blob 存储、OpenStack Swift 以及任何与 S3 兼容的对象存储。这为用户提供了一种廉价、耐用的方式来存储用于长期分析的指标。
5. `通过复制实现高可用性`：高可用性是 Grafana Mimir 的另一个关键特性。系统复制传入的指标，确保在机器发生故障时不会丢失任何数据。其水平可扩展架构还意味着它可以在零停机的情况下重启、升级或降级，确保指标摄取或查询不会中断。
6. `原生多租户`：Grafana Mimir 的原生多租户允许独立团队或业务部门的数据和查询隔离，使这些组可以共享同一个集群。高级限制和服务质量控制确保容量在租户之间公平共享，使其成为拥有多个团队和部门的大型组织的绝佳选择。

##### 服务部署

helm部署仓库：[mimir/operations/helm at main · grafana/mimir](https://github.com/grafana/mimir/tree/main/operations/helm)

```bash
[root@localhost thanos]# kubectl get pod -n mimir 
NAME                                        READY   STATUS             RESTARTS   AGE
mimir-alertmanager-0                        1/1     Running            0          3d1h
mimir-compactor-0                           1/1     Running            0          3d1h
mimir-distributor-55d4d6c56-pbj99           1/1     Running            0          3d1h
mimir-ingester-zone-a-0                     1/1     Running            0          3d1h
mimir-ingester-zone-b-0                     1/1     Running            0          3d1h
mimir-ingester-zone-c-0                     1/1     Running            0          3d1h
mimir-make-minio-buckets-5.4.0-5tbz5        0/1     Completed          0          3d1h
mimir-minio-57c55c679-rpmkw                 1/1     Running            0          3d1h
mimir-nginx-6f77d6f78-fkx9w                 1/1     Running            0          3d1h
mimir-overrides-exporter-6d97f959c5-sqwbq   1/1     Running            0          3d1h
mimir-querier-5fffbb78bd-588wn              1/1     Running            0          3d1h
mimir-querier-5fffbb78bd-8vz2g              1/1     Running            0          3d1h
mimir-query-frontend-69dbcf44c5-6qmvz       1/1     Running            0          3d1h
mimir-query-scheduler-698bdc98d6-9vg9n      1/1     Running            0          3d1h
mimir-query-scheduler-698bdc98d6-bs4vk      1/1     Running            0          3d1h
mimir-rollout-operator-744b68588-pjngz      0/1     Running            0          3d1h
mimir-ruler-7959b6bdd9-68jjh                1/1     Running            0          3d1h
mimir-store-gateway-zone-a-0                1/1     Running            0          3d1h
mimir-store-gateway-zone-b-0                1/1     Running            0          3d1h
mimir-store-gateway-zone-c-0                1/1     Running            0          3d1h
```

##### Mimir 服务架构

![mimir-1-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/mimir-1-2025-03-18.png)

- `Distributor`：Distributor 是一个无状态组件，它从 Prometheus 或 Grafana Alloy 接收时间序列数据。然后，Distributor 将数据分成批次，并并行发送到多个 ingester，在 ingester 之间分片序列，并按配置的复制因子复制每个序列。
- `Ingester`： 接受Distributor传入的样本，并将接收到的样本附加到存储在本地磁盘上的特定于租户的 TSDB，每两小时会定期刷新到磁盘，将新创建的块都会上传到长期存储。
- `Compactor`：将来自多个 Ingester的块合并到一个块中，并删除重复的样本。块压缩显着降低了存储利用率。
  - 垂直 Compaction 将 Ingester 同一时间范围（默认情况下为 2 小时范围）上传的租户的所有块合并为单个块。它还会删除最初写入 N 个块的样本的重复数据，这是复制的结果。垂直 Compaction 将单个时间范围的块数从 Ingester 的数量减少到每个租户一个块。
  - 水平 Compaction 在垂直 Compaction 后触发。它将具有相邻范围周期的多个块压缩成单个更大的块。水平 Compaction 后，关联块数据块的总大小不会改变。水平 Compaction 可以显着减小 Store-gateway 保存在内存中的索引和 index-header 的大小。
- `Query-frontend`：将较长时间范围的查询拆分为多个较小的查询。如果查询结果已缓存，则 Query-frontend（查询前端）返回缓存的结果。无法从结果缓存回答的查询将放入 Query-frontend内的内存队列中。此外聚合Querier查询的数据结果。

- `Querier`：充当 worker，从队列中拉取查询，并根据查询范围向每个匹配的 store-gateway 实例发送请求。

- `Store-gateway`：store-gateway 需要长期存储中 bucket 的视图。store-gateway 通过定期下载 bucket 索引 来保持 bucket 视图更新; 块分片和复制,块在多个 store-gateway 实例之间复制,块复制用于防止因某些块在给定时间未被任何 store-gateway 实例加载而导致的查询失败，例如在 store-gateway 故障或重启 store-gateway 实例时（例如，在滚动更新期间）。Store-gateway 加载属于其 store-gateway 分片的每个块的索引头。在 store-gateway 加载块的索引头后，该块即可被 querier 查询。当 querier 通过 store-gateway 查询块时，响应包含查询的块 ID 列表。如果 querier 尝试查询 store-gateway 未加载的块，则 querier 会在不同的 store-gateway 上重试查询，最多达到 -store-gateway.sharding-ring.replication-factor 值，默认值为 3。如果无法从任何副本成功查询块，则查询失败。参考 [Grafana Mimir store-gateway | Grafana Mimir 文档 - Grafana](https://grafana.org.cn/docs/mimir/latest/references/architecture/components/store-gateway/)

- `Alertmanager`: 可选组件，接受来自 Mimir ruler 的警报通知。Alertmanager 对警报通知进行去重和分组，并将它们路由到通知通道，例如电子邮件、PagerDuty 或 OpsGenie。

- `Overrides-exporter`: 防止单个租户使用过多的资源,例如速率、series总量等。
  
- `Query-scheduler`： 是一个可选的、无状态的组件，它保留一个待执行的查询队列，并将工作负载分配给可用的 queriers。参考 [Grafana Mimir query-scheduler](https://grafana.org.cn/docs/mimir/latest/references/architecture/components/query-scheduler/)
    ![image-4-2025-03-19](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/image-4-2025-03-19.png)

- `Ruler`: 是一个可选组件，用于评估记录和告警规则中定义的 PromQL 表达式。每个租户都有一组记录和告警规则，并且可以将这些规则分组到命名空间中。

#### Thanos

##### Thanos 功能特性

- `对象存储集成`：可以将 Prometheus 数据存储在对象存储中，如 S3、GCS 等，解决了 Prometheus 本地存储的容量限制问题，实现数据的长期存储和高可用性。
- `全局查询`：通过 Querier 组件，能够对多个 Prometheus 实例的数据进行全局查询和聚合，提供统一的查询入口，方便用户在大规模分布式环境中查询和分析监控数据。
- `数据复制与一致性`：支持数据的复制和一致性保证，确保在多个 Prometheus 实例和存储之间数据的准确性和完整性，提高了数据的可靠性。
- `水平扩展`：可以轻松地进行水平扩展，通过添加更多的组件实例来处理不断增长的监控数据量和查询请求，满足大规模集群监控的需求。
- `与 Prometheus 集成`：与 Prometheus 紧密集成，能够无缝地读取 Prometheus 的指标数据，并且可以利用 Prometheus 的现有生态和配置，降低了用户的使用成本和学习门槛。

##### Thanos 安装部署

Thanos Receiver安装部署

部署仓库：<https://github.com/thanos-io/kube-thanos>

```bash
thanos-query-6bc44896cd-bnk7t            1/1     Running   0          4d5h
thanos-receive-ingestor-default-0        1/1     Running   0          4d5h
thanos-receive-router-65df965665-bhfcr   1/1     Running   0          4d5h
thanos-store-0                           1/1     Running   0          4d5h
```

##### Thanos 服务架构

存储使用： 对象存储， 文件存储（测试阶段，不建议生产环境使用）

#### Reciver模式

![thanos-1-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/thanos-1-2025-03-18.png)

**Sidecar模式**
![thanos-2-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/thanos-2-2025-03-18.png)

- `Sidecar`：连接到Prometheus，读取其数据进行查询和/或将其上传到云存储。
- `Store Gateway`：提供云存储桶内部的指标。
- `Compactor`：对存储在云存储桶中的数据进行压缩、下采样和保留。
- `Receiver`：接收来自Prometheus远程写提前写日志的数据，将其公开，并/或将其上传到云存储。
- `Ruler/Rule`：针对prometheus中的数据评估记录和警报规则，以便展示和/或上传。
- `Querier/Query`：实现Prometheus的v1 API，从底层组件聚合数据。
- `Query Frontend`：实现Prometheus的v1 API，将其代理到Querier，同时缓存响应，并可选择按每天的查询拆分它。

#### VictoriaMetrics

Prometheus 的长期存储或作为 Grafana 中 Prometheus 和 Graphite 的替代品。

Api接口文档：[查询 API – VictoriaMetrics 中文手册](https://victoriametrics.com.cn/docs/query/api/)

##### 功能特性

- 兼容 PromQL，直接替代 Prometheus 存储。
- 支持 Prometheus 远程读写、长期存储（本地或云存储）。
- `最小内存占用`：处理数百万个独特的时间序列，所需内存比 InfluxDB 少 10 倍，比 Prometheus、Thanos 或 Cortex 少最多 7 倍。
- `高度可扩展性和数据摄取与查询性能`: 性能比 InfluxDB 和 TimescaleDB 高 20 倍。
- `高数据压缩`：存储点数比 TimescaleDB 多 70 倍的有限存储空间，所需存储空间比 Prometheus、Thanos 或 Cortex 少 7 倍。
- `降低存储成本`：根据 Grammarly 案例研究，比 Graphite 高效 10 倍。
- `单节点 VictoriaMetrics 可替代中等规模的集群`:这些集群是使用 Thanos、M3DB、Cortex、InfluxDB 或 TimescaleDB 等竞争解决方案构建的。参见 VictoriaMetrics 与 Thanos、衡量垂直可扩展性、远程写入存储战争 - PromCon 2019。
- `优化存储`：在高延迟 IO 和低 IOPS（HDD 和 AWS、Google Cloud、Microsoft Azure 等的网络存储）上运行良好。

##### 服务架构

![vm-1-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/vm-1-2025-03-18.png)

##### 服务组件

VictoriaMetrics生态系统除了单节点的VictoriaMetrics外，还包含以下组件：

- `vmagent` - 一个轻量级代理，用于通过基于拉取和推送协议接收指标，对其进行转换并发送到已配置的与 Prometheus 兼容的远程存储系统（如 VictoriaMetrics）。
- `vmalert` - 用于处理与 Prometheus 兼容的告警和记录规则的服务。
- `vmalert-tool` - 用于验证告警和记录规则的工具。
- `vmauth` - 针对 VictoriaMetrics 产品优化的授权代理和负载均衡器。
- `vmgateway` - 带有按租户速率限制功能的授权代理。
- `vmctl` - 用于在不同存储系统之间迁移和复制指标数据的工具。
- `vmbackup`、`vmrestore` 和 `vmbackupmanager` - 用于为VictoriaMetrics指标数据创建备份和从备份恢复的工具。
- `vminsert`、`vmselect` 和 `vmstorage` - VictoriaMetrics集群的组件。
  - `vmstorage` - 存储原始数据，并在给定的时间范围内返回符合指定标签过滤器的数据  
  - `vminsert` - 接收摄入的数据，并根据指标名称及其所有标签的一致性哈希算法将其分发到 vmstorage 节点中  
  - `vmselect` - 通过从所有已配置的 vmstorage 节点中获取所需数据来执行传入查询
- `VictoriaLogs` - 用户友好且成本效益高的日志数据库。

##### 安装部署

单机版ui：<http://***********:32494/vmui/>
集群版ui：<http://***********:30787/select/0/vmui/>

单机版安装：[Kubernetes monitoring via VictoriaMetrics Single](https://docs.victoriametrics.com/guides/k8s-monitoring-via-vm-single/)

```bash
git clone https://github.com/VictoriaMetrics/helm-charts.git
cd vm/helm-charts/charts/victoria-metrics-single
helm dependency build
cd ..
helm install vmsingle victoria-metrics-single/

[root@localhost charts]# kubectl get pod 
NAME                                        READY   STATUS    RESTARTS   AGE
vmsingle-victoria-metrics-single-server-0   1/1     Running   0          3m20s
```

集群版安装：[Kubernetes monitoring with VictoriaMetrics Cluster](https://docs.victoriametrics.com/guides/k8s-monitoring-via-vm-cluster/)

```bash
vm-cluster-victoria-metrics-cluster-vminsert-75c498d4f6-57k5c   1/1     Running   0          6d18h
vm-cluster-victoria-metrics-cluster-vminsert-75c498d4f6-fpwvr   1/1     Running   0          6d18h
vm-cluster-victoria-metrics-cluster-vmselect-558957f8f5-rszgm   1/1     Running   0          22h
vm-cluster-victoria-metrics-cluster-vmselect-558957f8f5-rvzzr   1/1     Running   0          6d18h
vm-cluster-victoria-metrics-cluster-vmselect-558957f8f5-tlpxq   1/1     Running   0          6d18h
vm-cluster-victoria-metrics-cluster-vmstorage-0                 1/1     Running   0          6d16h
vm-cluster-victoria-metrics-cluster-vmstorage-1                 1/1     Running   0          6d16h

# 文件存储 
# 集群版
[root@localhost ~]# ll /opt/local-path-provisioner/pvc-ce818a4b-7dc9-4d9c-b2ed-c3dcf35edbe2_default_server-volume-vmsingle-victoria-metrics-single-server-0
total 0
drwxr-xr-x 6 <USER> <GROUP> 214 Mar 11 14:13 cache
drwxr-xr-x 4 <USER> <GROUP>  30 Mar 10 19:52 data
-rw-r--r-- 1 <USER> <GROUP>   0 Mar 11 14:13 flock.lock
drwxr-xr-x 6 <USER> <GROUP>  95 Mar 10 19:52 indexdb
drwxr-xr-x 2 <USER> <GROUP>  43 Mar 10 19:52 metadata
drwxr-xr-x 2 <USER> <GROUP>   6 Mar 10 19:52 snapshots
drwxr-xr-x 3 <USER> <GROUP>  27 Mar 11 14:13 tmp
# 单机版
[root@localhost ~]# ll /opt/local-path-provisioner/pvc-9cee66d9-24a5-4012-9de3-13c65e221339_vm_vmstorage-volume-vm-cluster-victoria-metrics-cluster-vmstorage-0
total 0
drwxr-xr-x 4 <USER> <GROUP> 30 Mar 11 15:24 data
-rw-r--r-- 1 <USER> <GROUP>  0 Mar 11 15:24 flock.lock
drwxr-xr-x 6 <USER> <GROUP> 95 Mar 11 15:24 indexdb
drwxr-xr-x 2 <USER> <GROUP> 43 Mar 11 15:24 metadata
drwxr-xr-x 2 <USER> <GROUP>  6 Mar 11 15:24 snapshots
```

##### VMalert

VMalert通过查询向vm查询告警指标的QL，然后将触发告警的信息发给alertmanager。

vmalert 命令行参数设置：

```bash
- args:
    - --datasource.url=http://************:8481/select/0/prometheus #vm查询接口
    - --envflag.enable
    - --envflag.prefix=VM_
    - --httpListenAddr=:8880
    - --loggerFormat=json
    - --notifier.url=http://************:9093/alertmanager  #alertmanager发送告警接口
    - --rule=/config/alert-rules.yaml  # 告警规则文件
    image: registry.cnbita.com:5000/wuyiqiang/victoriametrics/vmalert:v1.112.0
```

验证结果：
![alert-2-2025-04-01](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/alert-2-2025-04-01.png)

监控告警可以正常触发。

##### VMagent

vmagent是一个替代Prometheus采集数据的解决方案，不提供可视化页面，资源消耗少，采集的数据可以上传到VM中。

vmagent 本身就提供metrics接口，设置不同级别的指标发现后，数据可以正常采集到。

vmaget配置的prometheus 配置，和当前的Prometheus配置不是完全一直的，存在某些字段的缺失。

功能验证结果如下：

![agent-2025-04-01](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/agent-2025-04-01.png)

部署的监控组件下面的networkpolicy需要清除掉，不然会出现请求超时的情况。

```bash
apiVersion: v1
data:
  scrape.yml: |2
    global:
      scrape_interval: 10s
      external_labels:
        cluster: localhost-vmagent   # 添加自定义标签
    scrape_configs:
    - job_name: vmagent
      static_configs:
      - targets:
        - localhost:8429
    - bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      job_name: kubernetes-apiservers
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      - action: keep
        regex: default;kubernetes;https
        source_labels:
        - __meta_kubernetes_namespace
        - __meta_kubernetes_service_name
        - __meta_kubernetes_endpoint_port_name
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        insecure_skip_verify: true
    - bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      job_name: kubernetes-nodes
      kubernetes_sd_configs:
      - role: node
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - replacement: kubernetes.default.svc:443
        target_label: __address__
      - regex: (.+)
        replacement: /api/v1/nodes/$1/proxy/metrics
        source_labels:
        - __meta_kubernetes_node_name
        target_label: __metrics_path__
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        insecure_skip_verify: true
    - bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      honor_timestamps: false
      job_name: kubernetes-nodes-cadvisor
      kubernetes_sd_configs:
      - role: node
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - replacement: kubernetes.default.svc:443
        target_label: __address__
      - regex: (.+)
        replacement: /api/v1/nodes/$1/proxy/metrics/cadvisor
        source_labels:
        - __meta_kubernetes_node_name
        target_label: __metrics_path__
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        insecure_skip_verify: true
    - job_name: kubernetes-service-endpoints
      kubernetes_sd_configs:
      - role: endpointslices
      relabel_configs:
      - action: drop
        regex: true
        source_labels:
        - __meta_kubernetes_pod_container_init
      - action: keep_if_equal
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_port
        - __meta_kubernetes_pod_container_port_number
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scrape
      - action: replace
        regex: (https?)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scheme
        target_label: __scheme__
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        source_labels:
        - __address__
        - __meta_kubernetes_service_annotation_prometheus_io_port
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - source_labels:
        - __meta_kubernetes_pod_name
        target_label: pod
      - source_labels:
        - __meta_kubernetes_pod_container_name
        target_label: container
      - source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - source_labels:
        - __meta_kubernetes_service_name
        target_label: service
      - replacement: ${1}
        source_labels:
        - __meta_kubernetes_service_name
        target_label: job
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
    - job_name: kubernetes-service-endpoints-slow
      kubernetes_sd_configs:
      - role: endpointslices
      relabel_configs:
      - action: drop
        regex: true
        source_labels:
        - __meta_kubernetes_pod_container_init
      - action: keep_if_equal
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_port
        - __meta_kubernetes_pod_container_port_number
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scrape_slow
      - action: replace
        regex: (https?)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scheme
        target_label: __scheme__
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        source_labels:
        - __address__
        - __meta_kubernetes_service_annotation_prometheus_io_port
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - source_labels:
        - __meta_kubernetes_pod_name
        target_label: pod
      - source_labels:
        - __meta_kubernetes_pod_container_name
        target_label: container
      - source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - source_labels:
        - __meta_kubernetes_service_name
        target_label: service
      - replacement: ${1}
        source_labels:
        - __meta_kubernetes_service_name
        target_label: job
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
      scrape_interval: 5m
      scrape_timeout: 30s
    - job_name: kubernetes-services
      kubernetes_sd_configs:
      - role: service
      metrics_path: /probe
      params:
        module:
        - http_2xx
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_probe
      - source_labels:
        - __address__
        target_label: __param_target
      - replacement: blackbox
        target_label: __address__
      - source_labels:
        - __param_target
        target_label: instance
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - source_labels:
        - __meta_kubernetes_service_name
        target_label: service
    - job_name: kubernetes-pods
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - action: drop
        regex: true
        source_labels:
        - __meta_kubernetes_pod_container_init
      - action: keep_if_equal
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_port
        - __meta_kubernetes_pod_container_port_number
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scrape
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        source_labels:
        - __address__
        - __meta_kubernetes_pod_annotation_prometheus_io_port
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels:
        - __meta_kubernetes_pod_name
        target_label: pod
      - source_labels:
        - __meta_kubernetes_pod_container_name
        target_label: container
      - source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/instance: vm-agent
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: victoria-metrics-agent
    app.kubernetes.io/version: v1.112.0
    helm.sh/chart: victoria-metrics-agent-0.16.2
  name: vm-agent-victoria-metrics-agent-config
  namespace: vm
```

#### Prometheus

### Prometheus 联邦

![prometheus-6-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/prometheus-6-2025-03-18.png)

验证Prometheus联邦的有效性，修改验证环境的Prometheus的配置文件。

```bash
additionalScrapeConfigs:
  name: prometheus-additional-scrape-configs
  key: scrape-configs.yaml
remoteWrite:
  - url: http://***********:30991/insert/0/prometheus
```

scrape-configs.yaml文件配置如下：

```bash
- job_name: 'federation'
  honor_labels: true
  scrape_interval: 30s
  metrics_path: '/federate'
  params:
    'match[]':
      - '{job=~"node-exporter|kube-state-metrics|kubelet|dcgm-exporter|npu-exporter|kubernetes-pods"}'
  static_configs:
    - targets:
        - '***********:41424' # dev69环境prometheus的访问地址。
```

验证结果：
![mutli-3-2025-04-08](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/mutli-3-2025-04-08.png)
![mutli-2-2025-04-08](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/mutli-2-2025-04-08.png)

通过在验证环境得Prometheus联邦开发69环境得Prometheus，数据可正常拉取展示。

### Prometheus Remote Write

![prometheus-7-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/prometheus-7-2025-03-18.png)

#### Cortex

### M3DB

#### PromscaleDB

#### TimescaleDB

### 方案功能测试对比验证

#### 指标查询接口响应格式对比

- Prometheus
  
```json
    {
        "status": "success",
        "data": {
            "resultType": "vector",
            "result": [
                {
                    "metric": {
                        "__name__": "kube_node_info",
                        "container": "kube-rbac-proxy-main",
                        "container_runtime_version": "docker://24.0.7",
                        "instance": "************:8443",
                        "internal_ip": "***********",
                        "job": "kube-state-metrics",
                        "kernel_version": "5.10.0-***********.oe2203sp2.x86_64",
                        "kubelet_version": "v1.25.14-heroes",
                        "kubeproxy_version": "deprecated",
                        "node": "yigou-dev-102-63",
                        "os_image": "openEuler 22.03 (LTS-SP2)",
                        "pod_cidr": "**********/24",
                        "system_uuid": "4f753042-aa1f-687e-4e88-a6f7653a2208"
                    },
                    "value": [
                        1742266951.102,
                        "1"
                    ]
                }
            ]
        }
    }
```

- Mimir
  
```json
    {
        "status": "success",
        "data": {
            "resultType": "vector",
            "result": [
                {
                    "metric": {
                        "__name__": "kube_node_info",
                        "cluster": "dev-69",
                        "container": "kube-rbac-proxy-main",
                        "container_runtime_version": "containerd://2.0.1",
                        "instance": "************:8443",
                        "internal_ip": "***********",
                        "job": "kube-state-metrics",
                        "kernel_version": "5.10.0-***********.oe2203sp2.x86_64",
                        "kubelet_version": "v1.25.14-heroes",
                        "kubeproxy_version": "deprecated",
                        "node": "yigou-dev-102-64",
                        "os_image": "openEuler 22.03 (LTS-SP2)",
                        "pod_cidr": "**********/24",
                        "prometheus": "monitoring/k8s",
                        "prometheus_replica": "prometheus-k8s-0",
                        "system_uuid": "91b33042-ff86-795c-00bc-f8359c8f14d7"
                    },
                    "value": [
                        1742201541.75,
                        "1"
                    ]
                }
            ]
        }
    }
```

- Thanos

```json
    {
        "status": "success",
        "data": {
            "resultType": "vector",
            "result": [
                {
                    "metric": {
                        "__name__": "kube_node_info",
                        "cluster": "dev-69",
                        "container": "kube-rbac-proxy-main",
                        "container_runtime_version": "containerd://2.0.1",
                        "instance": "************:8443",
                        "internal_ip": "***********",
                        "job": "kube-state-metrics",
                        "kernel_version": "5.10.0-***********.oe2203sp2.x86_64",
                        "kubelet_version": "v1.25.14-heroes",
                        "kubeproxy_version": "deprecated",
                        "node": "yigou-dev-102-64",
                        "os_image": "openEuler 22.03 (LTS-SP2)",
                        "pod_cidr": "**********/24",
                        "prometheus": "monitoring/k8s",
                        "receive": "true",
                        "replica": "thanos-receive-ingestor-default-0",
                        "system_uuid": "91b33042-ff86-795c-00bc-f8359c8f14d7",
                        "tenant_id": "default-tenant"
                    },
                    "value": [
                        1742201642.086,
                        "1"
                    ]
                }
            ]
        }
    }
```

- VictoriaMetrics
  
```json
    {
        "status": "success",
        "isPartial": false,
        "data": {
            "resultType": "vector",
            "result": [
                {
                    "metric": {
                        "__name__": "kube_node_info",
                        "cluster": "dev-69",
                        "container": "kube-rbac-proxy-main",
                        "container_runtime_version": "docker://24.0.7",
                        "instance": "************:8443",
                        "internal_ip": "***********",
                        "job": "kube-state-metrics",
                        "kernel_version": "5.10.0-************.oe2203sp2.x86_64",
                        "kubelet_version": "v1.25.14-heroes",
                        "kubeproxy_version": "deprecated",
                        "node": "yigou-dev-102-61",
                        "os_image": "openEuler 22.03 (LTS-SP2)",
                        "pod_cidr": "**********/24",
                        "prometheus": "monitoring/k8s",
                        "prometheus_replica": "prometheus-k8s-0",
                        "system_uuid": "d6eb3042-b2bd-8d0d-e811-6728f8bf6f06"
                    },
                    "value": [
                        1742201708,
                        "1"
                    ]
                }
            ]
        },
        "stats": {
            "seriesFetched": "12",
            "executionTimeMsec": 29
        }
    }
```

#### 指标查询性能对比

50并发一分钟压力测试（数据量12条）

| 接口                                                                               | 名称       | 总请求数 | 失败数 | 失败率 | 平均响应时间(ms) | 最小响应(ms) | 最大响应(ms) | 90% (ms) | 95% (ms) | 99% (ms) | TPS   | 平均接收流量 | 每秒请求数 |
| ---------------------------------------------------------------------------------- | ---------- | -------- | ------ | ------ | ---------------- | ------------ | ------------ | -------- | -------- | -------- | ----- | ------------ | ---------- |
| GET <http://***********:32566/prometheus/api/v1/query?query=kube_node_info>          | mimir      | 576      | 0      | 0%     | 145.91           | 0            | 3310         | 166      | 578      | 2959     | 10.71 | 63.71        | 9.09       |
| GET <http://***********:31763/api/v1/query?query=kube_node_info>                     | thanos     | 543      | 0      | 0%     | 139.27           | 0            | 4258         | 131      | 518      | 2406     | 10.05 | 65.51        | 8.6        |
| GET <http://***********:30787/select/0/prometheus/api/v1/query?query=kube_node_info> | cluster-vm | 552      | 0      | 0%     | 153.73           | 0            | 4003         | 236      | 900      | 2654     | 10.17 | 61.63        | 8.76       |
| GET <http://***********:32494/prometheus/api/v1/query?query=kube_node_info>          | sig - vm   | 545      | 0      | 0%     | 88.95            | 0            | 3645         | 13       | 401      | 1866     | 10.08 | 60.88        | 8.68       |

50并发一小时压力测试（数据量2400条）

| 接口                                                                                                                              | 名称                | 总请求数 | 失败数 | 失败率 | 平均响应时间(ms) | 最小响应(ms) | 最大响应(ms) | 90% (ms) | 95% (ms) | 99% (ms) | TPS  | 平均接收流量 | 每秒请求数 |
| --------------------------------------------------------------------------------------------------------------------------------- | ------------------- | -------- | ------ | ------ | ---------------- | ------------ | ------------ | -------- | -------- | -------- | ---- | ------------ | ---------- |
| GET <http://***********:32566/prometheus/api/v1/query_range?query=kube_node_info&start=1742186980&end=1742189980&step=15s>          | mimir-范围查询      | 468      | 0      | 0%     | 250.39           | 0            | 3425         | 461      | 1147     | 2492     | 8.82 | 354.94       | 9.40       |
| GET <http://***********:31763/api/v1/query_range?query=kube_node_info&start=1742186980&end=1742189980&step=15s>                     | thanos-范围查询     | 522      | 0      | 0%     | 401.7            | 43           | 3020         | 382      | 1722     | 2469     | 9.9  | 401.06       | 8.45       |
| GET <http://***********:30787/select/0/prometheus/api/v1/query_range?query=kube_node_info&start=1742186980&end=1742189980&step=15s> | cluster-vm-范围查询 | 480      | 0      | 0%     | 337              | 0            | 3748         | 184      | 1510     | 3484     | 9.04 | 360.63       | 7.69       |
| GET <http://***********:32494/prometheus/api/v1/query_range?query=kube_node_info&start=1742186980&end=1742189980&step=15s>          | sig-vm-范围查询     | 525      | 0      | 0%     | 316.31           | 25           | 4086         | 829      | 1489     | 3070     | 9.56 | 398.02       | 8.49       |

50并发一分钟压力测试（数据量2600条）复杂语句

| 接口                                                                                                                                                                                                                                                                                       | 名称                         | 总请求数 | 失败数 | 失败率 | 平均响应时间(ms) | 最小响应(ms) | 最大响应(ms) | 90% (ms) | 95% (ms) | 99% (ms) | TPS  | 平均接收流量 | 每秒请求数 |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------- | -------- | ------ | ------ | ---------------- | ------------ | ------------ | -------- | -------- | -------- | ---- | ------------ | ---------- |
| GET <http://***********:32566/prometheus/api/v1/query_range?query=sum(kube_pod_status_phase{phase="Running"}> * on (pod) group_left(namespace, resource) sum(kube_pod_container_resource_requests{resource="cpu"}) by (pod)) by (resource)&start=1742150980&end=1742189980&step=15s          | mimir-范围查询-复杂语句      | 518      | 0      | 0%     | 1116.39          | 462          | 3660         | 1292     | 2407     | 3341     | 9.51 | 701.36       | 8.31       |
| GET <http://***********:31763/api/v1/query_range?query=sum(kube_pod_status_phase{phase="Running"}> * on (pod) group_left(namespace, resource) sum(kube_pod_container_resource_requests{resource="cpu"}) by (pod)) by (resource)&start=1742150980&end=1742189980&step=15s                     | thanos-范围查询-复杂语句     | 381      | 0      | 0%     | 5687.61          | 2240         | 8653         | 5810     | 6531     | 7846     | 6.9  | 469.76       | 6.26       |
| GET <http://***********:30787/select/0/prometheus/api/v1/query_range?query=sum(kube_pod_status_phase{phase="Running"}> * on (pod) group_left(namespace, resource) sum(kube_pod_container_resource_requests{resource="cpu"}) by (pod)) by (resource)&start=1742150980&end=1742189980&step=15s | cluster-vm-范围查找-复杂语句 | 521      | 0      | 0%     | 572.9            | 0            | 3480         | 613      | 1703     | 2879     | 9.63 | 707.54       | 8.37       |
| GET <http://***********:32494/prometheus/api/v1/query_range?query=sum(kube_pod_status_phase{phase="Running"}> * on (pod) group_left(namespace, resource) sum(kube_pod_container_resource_requests{resource="cpu"}) by (pod)) by (resource)&start=1742150980&end=1742189980&step=15s          | sig-vm-范围查询-复杂语句     | 512      | 0      | 0%     | 558.05           | 0            | 2871         | 936      | 1804     | 2314     | 9.66 | 701.68       | 8.29       |

#### 存储空间占用对比

| 名称             | 存储方式 | 存储用量             |
| ---------------- | -------- | -------------------- |
| mimir            | 对象存储 | 928Mi                |
| thanos           | 对象存储 | 数据无法写入对象存储 |
| cluster-vm       | 文件存储 | 135Mi + 155Mi        |
| sig-vm           | 文件存储 | 438M                 |
| prometheus-local | 文件存储 | 609M                 |
| prometheus-dev69 | 文件存储 | 2.9G                 |

#### Grafana 面板集成可行性验证

![grafana-1-2025-03-18](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/grafana-1-2025-03-18.png)

对比服务都可与grafana面板进行集成

#### 资源消耗对比

部署metrics server 服务，并在执行复杂语句查询过程中进行cpu和内存资源占用检测，结果如下：

| thanos     | grafana mimir | vm cluster  | vm signal  |
| ---------- | ------------- | ----------- | ---------- |
| 70m / 1.1G | 3500m / 4.5G  | 130m / 1.8G | 40m / 600m |

### 子集群和主控网络断链后数据可恢复时间验证

验证方式：将vm insert的副本数量将为0，抵达测试时间后重新设置副本数量，然后观察这段数据能否正常采集到。

- `十分钟数据空挡`：数据可以重写到远程存储，dev和local环境数据都正常写入。

- `一个小时的数据空挡`：数据可以重写到远程存储，dev和local环境数据都正常写入。

    ![data-2025-04-01](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/data-2025-04-01.png)
    ![data-1-2025-04-01](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/data-1-2025-04-01.png)

观测的情况是数据恢复的速度很快，几秒钟即可。

官方描述是最大支持两个小时的数据恢复，参考 <https://prometheus.io/docs/practices/remote_write/>

## 参考文档

[Prometheus多集群监控的3种方案，你选哪种？_后端_华为云开发者联盟_InfoQ写作社区](https://xie.infoq.cn/article/05daf94e28fdfe76ce18f6ef1)
[Scale Prometheus：K8s 部署 GreptimeDB 集群作为 Prometheus 长期存储 | Greptime](https://www.greptime.cn/blogs/2024-09-26-scale-prometheus)
[Thanos 与 VictoriaMetrics，谁才是打造大型 Prometheus 监控系统的王者？-腾讯云开发者社区-腾讯云 (tencent.com)](https://cloud.tencent.com/developer/article/1690227)
[prometheus高可用解决方案（VictoriaMetrics ）_prometheus 高可用-CSDN博客](https://blog.csdn.net/litaimin/article/details/141318450)
[使用 Thanos 集中管理多 Prometheus 实例数据 - 文章 - 开发者社区 - 火山引擎]([volcengine.com](https://developer.volcengine.com/articles/7391689032658321446))
[部署 Thanos Receive 模式实现 Prometheus 多集群管理-CSDN博客](https://developer.volcengine.com/articles/7391689032658321446)
[kube-prometheus/manifests at main · prometheus-operator/kube-prometheus](https://github.com/prometheus-operator/kube-prometheus/tree/main/manifests)
[helm-charts/charts/kube-prometheus-stack/values.yaml at main · prometheus-community/helm-charts](https://github.com/prometheus-community/helm-charts/blob/main/charts/kube-prometheus-stack/values.yaml)
[使用 MinIO 与 Grafana Mimir 实现指标持久化存储-腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/2314693)
[Get started with Grafana Mimir and GEM using the Helm chart | Grafana Labs Helm charts documentation](https://grafana.com/docs/helm-charts/mimir-distributed/latest/get-started-helm-charts/)
[APO使用场景之：统一的指标采集展示Grafana Alloy介绍 Alloy是Grafana 发布替代之前Grafan - 掘金](https://juejin.cn/post/7413274784484229132)
[VictoriaMetrics 单点模式_victoriametrics docker-CSDN博客](https://blog.csdn.net/qq_34556414/article/details/125621536)
[VictoriaMetrics 集群模式部署_victoriametrics 集群部署-CSDN博客](https://blog.csdn.net/qq_34556414/article/details/125722088)
[使用thanos、prometheus和minio打造高可用监控平台 - 简书](https://www.jianshu.com/p/45153d5b2a25)
