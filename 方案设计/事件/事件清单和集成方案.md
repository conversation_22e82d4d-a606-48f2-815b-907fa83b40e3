# 类脑云事件模块方案

在k8s 集群中，event的活动量很大，不太可能长时间存储在etcd中，默认情况下，Event留存时间也只有1小时。当我们使用kubectl describe获取一个对象时，可能因时间超限而无法获取它的历史事件，这对集群的使用者非常的不友好。除了能查看集群事件外，我们可能还有类似追踪一些特定的Warning事件（如Pod生命周期、副本集或worker节点状态）来进行相关告警的需求。

待解决问题：

1. pod事件增加到相关作业的事件里（如任务启动前的镜像拉取动作/行为，需要考虑如何告知用户，目前镜像拉取前的事件是”调度成功“对用户来说会有任务调度成功但是未启动的错误感知。

## 事件清单

traingJob/NoteBook

| 事件名称                      | 事件信息（Message）                                                                                                     |
| ----------------------------- | ----------------------------------------------------------------------------------------------------------------------- |
| Starting                      | starting handle                                                                                                         |
| CreateVolcanoJobSuccess       | volcanoJob create successfully                                                                                          |
| CreateVolcanoJobFailed        | volcanoJob create failed: %s                                                                                            |
| PullImageSuccess              | image pull successfully                                                                                                 |
| PullImageFailed               | [error message]                                                                                                         |
| Success                       | server is running                                                                                                       |
| Failed                        | server failed: %s                                                                                                       |
| Completed                     | server is completed                                                                                                     |
| SchedulerSuccess              | server is scheduled successfully                                                                                        |
| SchedulerFailed               | server is scheduled failed: %s                                                                                          |
| WaitingScheduler              | task waiting                                                                                                            |
| Stopping                      | server is stopping                                                                                                      |
| Stopped                       | server is stopped successfully                                                                                          |
| Checked                       | plugin [%s] env does not exist                                                                                          |
| SetMaxRunTime                 | maxRunTime is set                                                                                                       |
| Restarting                    | The job is restarting at version %d due to %+v                                                                          |
| Unrestartable                 | The job cannot be restarted at version %d, it either does not match any restart policies or has reached the retry limit |
| NodeHealthCheckingBeforeStart | Checking node health before starting the job at version %d                                                              |
| NodeHealthyBeforeStart        | All nodes are healthy before starting the job at version %d                                                             |
| NodeUnhealthyBeforeStart      | There are unhealthy nodes before starting the job at version %d                                                         |
| JobHangHealthCheckStarted     | Job hang health check started at version %s                                                                             |
| JobHangHealthCheckStopped     | Job hang health check stopped at version %s                                                                             |
| JobHangDetected               | The job has been hanging at version %d for over %d seconds                                                              |
| FailedJobRestartableChecking  | Verifying if the failed job at version %d is restartable                                                                |
| FailedJobRestartable          | The failed job is restartable at version %d, as verified by the %s policy                                               |
| FailedJobUnrestartable        | The failed job is unrestartable at version %d, as verified by the %s policy                                             |
|                               | task %s pod %s is err, message: %s                                                                                      |

TensorBoard

| 事件名称         | 事件信息（Message）                         |
| ---------------- | ------------------------------------------- |
| MaxRunningTimeUp | tensorboard's maxRunningTime is up          |
| PodRunning       | Pod start running                           |
| Ingress          | Ready"                                      |
| podStoped        | tensorboard's pod have deleted              |
| PodCreated       | pod is created, waiting for pod running     |
| ServiceCreated   | service is created, waiting for pod running |
| IngressCreated   | ingress is created, waiting for pod running |

ImageMaker

| 事件名称            | 事件信息（Message）                         |
| ------------------- | ------------------------------------------- |
| ToolPodCreated      | Tool pod created                            |
| ImageMakerSucceeded | Image maker succeeded                       |
| ImageMakerStopped   | Image maker stopped"                        |
| ToolPodFailed       | Tool pod failed                             |
| ImageMakerFailed    | Image maker failed: [error message]         |
| StopFailed          | Failed to stop image maker: [error message] |

Application

| 事件名称   | 事件信息（Message）                 |
| ---------- | ----------------------------------- |
| Creating   | Creating Application                |
| Restarting | Restarting Application              |
| Running    | Application Running                 |
| Stopping   | Stopping Application                |
| Listening  | Application Listening               |
| Failed     | Application Failed: [error message] |

Serverless-App

| 事件名称                      | 事件信息（Message）                             |
| ----------------------------- | ----------------------------------------------- |
| Creating                      | Creating ServerlessApp Resource                 |
| Listening                     | ServerlessApp Resource Listening                |
| Failed                        | ServerlessApp Resource Failed: %v               |
| Stoping                       | Failed to stop app:%v                           |
| FailedUpdateStatus            | Failed to update status during create phase: %v |
| FaiedCreatKsvc                | Knative service Failed to create :%v            |
| CreatedKsvc                   | Knative service create success                  |
| KsvcfalseAndDeploymentUnready | Knative service Ready is false :%v              |
| Running                       | ServerlessApp Resource is running               |

Volume

| 事件名称 | 事件信息（Message）       |
| -------- | ------------------------- |
| Creating | Creating dataset          |
| Failed   | [error message]           |
| Success  | Create dataset successful |

## event-exporter 功能验证

git 仓库：<https://github.com/resmoio/kubernetes-event-exporter/tree/master>

event-exporter的配置文件如下：

```bash
apiVersion: v1
kind: ConfigMap
metadata:
  name: event-exporter-cfg
  namespace: monitoring
data:
  config.yaml: |
    logLevel: debug
    logFormat: json
    kubeQPS: 60                
    kubeBurst: 60
    maxEventAgeSeconds: 60
    metricsNamePrefix: event_exporter_
    route:
      routes:
        - match:
            - receiver: "loki"
    receivers:
      - name: "loki"
        loki: 
          streamLabels:
            foo: bar
          url: "http://loki.monitoring.svc:3100/loki/api/v1/push"
```

验证结果如下：

![image-2025-06-23](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/image-2025-06-23.png)

```bash
{
  "metadata": {
    "name": "a16093115473063936596769.184b8be3f60b2801",
    "namespace": "hero-user",
    "uid": "fff334f9-60cc-48fe-9ed7-6c8cc1e27253",
    "resourceVersion": "873395388",
    "creationTimestamp": "2025-06-23T02:44:51Z",
    "labels": {
      "system.hero.ai": "event"
    }
  },
  "reason": "SchedulerFailed",
  "message": "1/1 tasks in gang unschedulable: pod group is not ready, 1 Pending, 1 minAvailable; Pending: 1 Unschedulable",
  "source": {
    "component": "Notebook"
  },
  "firstTimestamp": "2025-06-23T02:44:51Z",
  "lastTimestamp": "2025-06-23T02:44:51Z",
  "type": "Warning",
  "eventTime": null,
  "reportingComponent": "",
  "reportingInstance": "",
  "clusterName": "",
  "involvedObject": {
    "kind": "Notebook",
    "namespace": "hero-user",
    "name": "a16093115473063936596769",
    "uid": "b86a62a3-2448-44fd-bbbc-6b9551143fad",
    "apiVersion": "system.hero.ai/v1alpha1",
    "resourceVersion": "873344869",
    "labels": {
      "business.system.id": "120273943618821996124",
      "job-sub-type.system.hero.ai": "training-debug",
      "job-type.system.hero.ai": "training",
      "notebooks.system.hero.ai/name": "a16093115473063936596769",
      "notebooks.system.hero.ai/namespace": "hero-user",
      "resourcepool.system.hero.ai": "default",
      "tenant.id": "120273943618847469891",
      "user.id": "9fedf53c3d784e74b9cf428c6825b78e"
    },
    "annotations": {
      "nodes.system.hero.ai": ""
    },
    "deleted": false
  }
}
{
  "metadata": {
    "name": "leaderworkerset-multi-template-0-2.184a15405ac946b5",
    "namespace": "migrate",
    "uid": "7d7627c8-47f6-47ba-acae-138d4c32a7d8",
    "resourceVersion": "873395234",
    "creationTimestamp": "2025-06-23T02:44:40Z"
  },
  "reason": "Pulling",
  "message": "Pulling image \"nginxinc/nginx-unprivileged:1.27\"",
  "source": {
    "component": "kubelet",
    "host": "yigou-dev-102-67"
  },
  "firstTimestamp": "2025-06-18T08:19:31Z",
  "lastTimestamp": "2025-06-23T02:44:40Z",
  "count": 1187,
  "type": "Normal",
  "eventTime": null,
  "reportingComponent": "kubelet",
  "reportingInstance": "yigou-dev-102-67",
  "clusterName": "",
  "involvedObject": {
    "kind": "Pod",
    "namespace": "migrate",
    "name": "leaderworkerset-multi-template-0-2",
    "uid": "0e5eab41-96c1-4e33-a7b0-c87d0093dd2c",
    "apiVersion": "v1",
    "resourceVersion": "868274514",
    "fieldPath": "spec.containers{nginx}",
    "labels": {
      "controller-revision-hash": "leaderworkerset-multi-template-0-6c6d8fcff5",
      "leaderworkerset.sigs.k8s.io/group-index": "0",
      "leaderworkerset.sigs.k8s.io/group-key": "08a017ab7fe50de079b8a0afe5e4f78539a6b408",
      "leaderworkerset.sigs.k8s.io/name": "leaderworkerset-multi-template",
      "leaderworkerset.sigs.k8s.io/template-revision-hash": "5974d859cb",
      "leaderworkerset.sigs.k8s.io/worker-index": "2",
      "statefulset.kubernetes.io/pod-name": "leaderworkerset-multi-template-0-2"
    },
    "annotations": {
      "cni.projectcalico.org/containerID": "bebeefab81a787522b97be575187b8bc1e44cf51509d3b870ab5ffe1f675f2d0",
      "cni.projectcalico.org/podIP": "*************/32",
      "cni.projectcalico.org/podIPs": "*************/32",
      "leaderworkerset.sigs.k8s.io/leader-name": "leaderworkerset-multi-template-0",
      "leaderworkerset.sigs.k8s.io/size": "4"
    },
    "ownerReferences": [
      {
        "apiVersion": "apps/v1",
        "kind": "StatefulSet",
        "name": "leaderworkerset-multi-template-0",
        "uid": "d389a36e-4fa7-41b7-9e7f-0dff0a8df48b",
        "controller": true,
        "blockOwnerDeletion": true
      }
    ],
    "deleted": false
  }
}
...
```

集群服务的事件信息可以正常采集到。

## 类脑云事件交互逻辑

### 系统组件

1. **resource-manager**：提供 `/event` 接口，根据任务ID从Loki服务中查询event-exporter采集的日志信息
2. **event-exporter**：采集集群事件并通过标准输出打印到Pod日志中（已进行定制化开发）
3. **job-controller**: 生成traingjob、notebook、imagemaker和tesorboard等事件信息。

### 数据流程

1. event-exporter采集Kubernetes CR事件
2. 事件数据通过api接口发送到Loki存储
3. resource-manager通过Loki接口根据任务ID查询相关事件
4. 前端展示查询到的事件信息（维护了对应关系和过滤规则）

通过loki查询到的事件信息如下图所示：

![事件查询流程](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/image-2025-06-19.png)

### 事件过滤机制

为减轻Loki存储压力，event-exporter新增了事件过滤逻辑：

1. 优先采集带有 `system.hero.ai` 标签的事件
2. 对于其他事件，仅采集满足以下全部条件的事件：
   - 事件对象类型为Pod
   - 命名空间为hero-user
   - Pod名称包含"pipeline-task"

```bash

func (e *EventWatcher) OnAdd(obj interface{}) {
    event := obj.(*corev1.Event)

    // 过滤逻辑
    if _, ok := event.Labels["system.hero.ai"]; !ok {
        if event.InvolvedObject.Kind != "Pod" || event.InvolvedObject.Namespace != "hero-user" {
            return
        }

        if event.InvolvedObject.Kind == "Pod" {
            if !strings.Contains(event.InvolvedObject.Name, "pipeline-task") {
                return
            }
        }
    }

    klog.Infof("event: %s", event.Name)
    e.eventQueue.Add(event)
}

```

### 需求解决方案

实现方式：在traingjob和notebook中新增如下事件：

事件名称：PullingImage
事件信息：pulling image %s

当容器状态时Waiting并且reason为PodInitializing时，记录事件。

```bash

if containerStatus.State.Waiting != nil {
  if strings.Contains(containerStatus.State.Waiting.Reason, "PodInitializing") {
      r.EventRecord.EventRecord(ctx, notebookjob, v1.EventTypeNormal, systemv1alpha1.EventImagePulling, fmt.Sprintf("pulling image %s", containerStatus.Image))
  }
...

```
