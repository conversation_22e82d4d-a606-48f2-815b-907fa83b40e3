# 多云监控方案

## 问题背景

在单集群部署场景中，promtail采集每个节点的日志数据并上传到loki存储中，客户端通过resource-manager访问loki中的日志数据。

![log-2025-04-07](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/log-2025-04-07.png)

在多集群部署场景中，子集群中的promtail采集每个节点的日志数据并上传到子集群中的loki存储中，客户端通过karmada访问resource-manager来访问子集群中loki中的日志数据。

![log-1-2025-04-07](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/log-1-2025-04-07.png)

在当前使用的日志方案中，loki的默认保存日志时长是180天，保存的数据量是TB级别的，这种方案存在如下问题：

1. 系统服务的日志是冗余的，保存180天是浪费资源的
2. 用户日志是重要的，需要保存较长时间。
3. 子集群与主控集群断联后，用户无法查询历史数据。

## 解决方案

针对**数据冗余**的问题，通过使用`日志分层`的方式，减少日志存储成本，提高用户日志的存储时间。

实现方式：

1. 将用户日志和系统服务日志分开存储，用户日志存储在用户loki中，系统服务日志存储在系统loki中(根据命名空间区分日志存储位置，并设置不同的保存时间)。客户端通过用户loki查询用户日志。
  ![Loki-11-2025-04-07](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/Loki-11-2025-04-07.png)

针对**子集群和主控断联，用户无法查询历史数据**的问题，通过`日志持久化`的方式进行解决。

实现方式：

1. 产品需要告知用户默认的日志保存时间，重要的日志需要持久化到个人存储。
2. 需要提供数据迁移的功能，在子集群下线的情况下，告知用户迁移数据到其它存储。

## 方案验证

### 竞品调研

华为云日志服务功能如下：
冷热日志分层存储/日志数据分析+可视化/日志告警/秒级搜索/模糊查询/日志上下文查看/实时查询

阿里云日志服务功能如下：
冷热日志分层存储/日志数据分析+可视化/日志告警/模糊查询/实时查询

### 技术选型

该技术选型，主要是想通过统一存储的方式去解决上述的问题，验证的技术选型如下：

#### promtail + loki + memchche

单集群服务架构如下所示：

![log-11-2025-04-07](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/log-11-2025-04-07.png)

多集群服务架构如下所示：
![log-14-2025-04-07](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/log-14-2025-04-07.png)

- `Promtail`: 每个节点部署副本，采集日志数据，并将数据发送到loki。
    promtail的数据采集来源如下：
  - `/var/log/pods/*/*.log`:  

    ```bash
    [root@local-94 charts]# ll /var/log/pods
    total 0
    drwxr-xr-x 3 <USER> <GROUP> 23 Mar  6 14:56 kube-system_calicoctl-xhs95_2094de66-6808-40fd-a99b-792658e16d71
    drwxr-xr-x 3 <USER> <GROUP> 37 Mar  6 14:56 kube-system_calico-kube-controllers-5955986f9c-m555d_6cacffa2-8cd3-4670-9a46-8544b381691a
    ...
    ```

  - `/var/log/journal`：需要配置journalctl日志持久化存储，不然重启之前的数据都是丢失的。

- `Loki`：存储数据，提供查询接口。
  - `Distributor`：日志写入入口，接收 Promtail/Agent 发送的日志；使用一致性哈希环分配日志到目标 Ingester；支持水平扩展，无状态设计。
  - `Ingester`：内存存储日志数据（按租户和标签索引）；定期将数据持久化到对象存储（S3/GCS 等）。
  - `Query Frontend`：查询入口，处理用户查询请求；支持缓存查询结果（本地 / Redis）；转发查询到 Query Scheduler。
  - `Querier`：优先查 Ingester 内存数据，然后在查询持久化数据。
- `Memchche`：提供缓存索引写入和查询，块缓存。

存在的问题：

1. 因promtail无法将数据发送到多个loki，所以当子集群和主控集群都提供查询服务的时候，子集群需要部署两套prometail，浪费资源；
2. 在多个子集群都发送数据到主控集群，对主控集群的网络造成很大压力。

##### 接口性能测试

对比在有无部署memcache的情况下，loki的查询接口性能差异。

| 接口                                                                                                                                                | 名称              | 总请求数 | 失败数 | 失败率 | 平均响应时间 | 最小响应 (ms) | 最大响应 (ms) | 90% (ms) | 95% (ms) | 99% (ms) | TPS  | 平均接收流量 | 每秒请求数 |
| --------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------- | -------- | ------ | ------ | ------------ | ------------- | ------------- | -------- | -------- | -------- | ---- | ------------ | ---------- |
| GET <http://10.0.102.94:31301/loki/api/v1/query_range?query={container="compactor"}> =``&start=1743492573284383211&end=1743492673284383211&limit=1000 | loki 原生接口查询 | 415      | 0      | 0%     | 91.47        | 0             | 320           | 110      | 197      | 274      | 7.4  | 1.37         | 6.86       |
| GET <http://10.0.102.94:32250/loki/api/v1/query_range?query={container="compactor"}> =``&start=1743492573284383211&end=1743492673284383211&limit=1000 | loki 原生接口查询 | 413      | 0      | 0%     | 87.47        | 53            | 370           | 97       | 180      | 281      | 7.27 | 1.34         | 6.74       |

通过grafana 获取memcache相关指标如下图：

![log-3-2025-04-07](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/log-3-2025-04-07.png)

结论： 在有无部署memcache的情况下，带有缓存的接口响应更快一些。

#### Elasticsearch + Filebeat + Kibana +  INFINI Console

[INFINI Console](https://github.com/infinilabs/console) 是一个轻量级的多集群、跨版本统一的Elasticsearch / Opensearch / Easysearch治理平台。

服务架构图如下：

![log-9-2025-04-07](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/log-9-2025-04-07.png)

EFK（Elasticsearch + Filebeat + Kibana）日志系统是一种常见的解决方案，根据抓取日志组件不同存在以下几种组合：

- ELK（Elasticsearch，Logstash，Kibana）
- EFK（Elasticsearch，Filebeat，Kibana）
- EFK（Elasticsearch，Fluentd，Kibana）

EFK部署安装流程如下：

```bash
helm repo add elastic https://helm.elastic.co

helm pull elastic/elasticsearch
helm install elasticsearch ./elasticsearch -n log

helm pull elastic/kibana
helm install kibana ./kibana -n log

helm pull elastic/filebeat
helm install filebeat ./filebeat -n log
```

EFK日志查询功能验证如下：

![log-4-2025-04-07](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/log-4-2025-04-07.png)

存在的问题：

1. 无法做到统一日志存储。

#### Elasticsearch + Filebeat + Kibana + Cross-Cluster Search

`跨集群搜索（cross-cluster search）`使你可以针对一个或多个远程集群运行单个搜索请求。这是Elasticsearch本身特性。

![log-8-2025-04-07](https://fourt-wyq.oss-cn-shanghai.aliyuncs.com/images/log-8-2025-04-07.png)

当我们的client向集群cluster_1发送请求时，它可以搜索自己本身的集群，同时也可以向另外的两个集群cluster_2及cluster_3发送请求。最后的结果由cluster_1返回给客户端。

存在的问题：

1. 无法做到统一日志存储。

#### Clickhouse

功能特性：

`大数据量`：CK 的分布式架构支持动态扩缩容，可支撑海量数据存储。
`写入性能`：CK 的 MergeTree 表的写入速度在200MB/s，具有很高吞吐，写入基本没有瓶颈。
`查询性能`：CK 支持分区索引和排序索引，具有很高的检索效率，单机每秒可扫描数百万行的数据。
`存储成本`：CK 基于列式存储，数据压缩比很高，同时基于HDFS做冷热分离，能够进一步地降低存储成本。

存在的问题：

1. 需要根据业务构建日志表。目前还没有开源处理基于k8s日志的方案。

## 待验证问题

# nil

## 参考文档
<https://mp.weixin.qq.com/s/byB9F9T7lCAEe9sqKEp_8w>
<https://mp.weixin.qq.com/s/J2jiuBm8nKTvF_hnDlkH3g>
<https://zhuanlan.zhihu.com/p/554103626>
<https://mp.weixin.qq.com/s/x2_T5zESi0nOW5k1ETZc7g>
