
# linux 配置服务代理

```bash
export http_proxy=http://*************:7890
export https_proxy=http://*************:7890
```

docker 配置代理

```bash
vi /usr/lib/systemd/system/docker.service


[Service]
...
# proxy
Environment="HTTP_PROXY=http://*************:7890"
Environment="HTTPS_PROXY=http://*************:7890"
Environment="NO_PROXY=localhost,127.0.0.1,172.17.*.*,10.0.*.*,registry.hub.com,registry.cnbita.com"


docker daemon-reload
systemctl restart docker

```
