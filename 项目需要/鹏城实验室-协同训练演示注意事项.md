
执行命令：每个环境执行

```bash
cd /data/LLAMA1; bash train_llama.sh <任务标识>
```

例如： cd /data/LLAMA1; bash train_llama.sh llama32

执行过程中出现如下问题：
现象：一个集群正常运行，一个集群执行失败。

```bash
2025-03-04 14:55:27：trainable params: 4,194,304 || all params: 6,742,609,920 || trainable%: 0.06220594176090199
2025-03-04 14:55:37：--> applying fsdp activation checkpointing...
2025-03-04 14:55:37：The repository for samsum contains custom code which must be executed to correctly load the dataset. You can inspect the repository content at https://hf.co/datasets/samsum.
2025-03-04 14:55:37：You can avoid this prompt in future by passing the argument `trust_remote_code=True`.
2025-03-04 14:55:37：Traceback (most recent call last):
2025-03-04 14:55:37：
2025-03-04 14:55:37：  File "/opt/conda/lib/python3.8/site-packages/datasets/load.py", line 125, in resolve_trust_remote_code
2025-03-04 14:55:37：    answer = input(
2025-03-04 14:55:37：EOFError: EOF when reading a line
2025-03-04 14:55:37：
2025-03-04 14:55:37：During handling of the above exception, another exception occurred:
2025-03-04 14:55:37：
2025-03-04 14:55:37：Traceback (most recent call last):
2025-03-04 14:55:37：  File "./finetuning.py", line 8, in <module>
2025-03-04 14:55:37：    fire.Fire(main)
2025-03-04 14:55:37：  File "/opt/conda/lib/python3.8/site-packages/fire/core.py", line 143, in Fire
2025-03-04 14:55:37：    component_trace = _Fire(component, args, parsed_flag_args, context, name)
2025-03-04 14:55:37：  File "/opt/conda/lib/python3.8/site-packages/fire/core.py", line 477, in _Fire
2025-0304 14:55:37：    component, remaining_args = _CallAndUpdateTrace(
2025-03-04 14:55:37：  File "/opt/conda/lib/python3.8/site-packages/fire/core.py", line 693, in _CallAndUpdateTrace
2025-03-04 14:55:37：    component = fn(*varargs, **kwargs)
2025-03-04 14:55:37：  File "/data/pclshenzhen/13570536-fortest001-fortest001/6b49eb57f059581bb6eb027cc7e0c1378f83a518/llama-recipes/src/llama_recipes/finetuning.py", line 205, in main
2025-03-04 14:55:37：    dataset_train = get_preprocessed_dataset(
2025-03-04 14:55:37：  File "/data/pclshenzhen/13570536-fortest001-fortest001/6b49eb57f059581bb6eb027cc7e0c1378f83a518/llama-recipes/src/llama_recipes/utils/dataset_utils.py", line 73, in get_preprocessed_dataset
2025-03-04 14:55:37：    return DATASET_PREPROC[dataset_config.dataset](
2025-03-04 14:55:37：  File "/data/pclshenzhen/13570536-fortest001-fortest001/6b49eb57f059581bb6eb027cc7e0c1378f83a518/llama-recipes/src/llama_recipes/datasets/samsum_dataset.py", line 11, in get_preprocessed_samsum
2025-03-04 14:55:37：    dataset = datasets.load_dataset(path="/data/LLAMA1/samsum", split=split)
2025-03-04 14:55:37：  File "/opt/conda/lib/python3.8/site-packages/datasets/load.py", line 2132, in load_dataset
2025-03-04 14:55:37：    builder_instance = load_dataset_builder(
2025-03-04 14:55:37：  File "/opt/conda/lib/python3.8/site-packages/datasets/load.py", line 1853, in load_dataset_builder
2025-03-04 14:55:37：    dataset_module = dataset_module_factory(
2025-03-04 14:55:37：  File "/opt/conda/lib/python3.8/site-packages/datasets/load.py", line 1575, in dataset_module_factory
2025-03-04 14:55:37：    return LocalDatasetModuleFactoryWithScript(
2025-03-04 14:55:37：  File "/opt/conda/lib/python3.8/site-packages/datasets/load.py", line 761, in get_module
2025-03-04 14:55:37：    trust_remote_code = resolve_trust_remote_code(self.trust_remote_code, self.name)
2025-03-04 14:55:37：  File "/opt/conda/lib/python3.8/site-packages/datasets/load.py", line 138, in resolve_trust_remote_code
2025-03-04 14:55:37：    raise ValueError(
2025-03-04 14:55:37：ValueError: The repository for samsum contains custom code which must be executed to correctly load the dataset. You can inspect the repository content at https://hf.co/datasets/samsum.
2025-03-04 14:55:37：Please pass the argument `trust_remote_code=True` to allow custom code to be run.
2025-03-04 14:55:42：[2025-03-04 14:55:42,818] torch.distributed.elastic.multiprocessing.api: [ERROR] failed (exitcode: 1) local_rank: 0 (pid: 95) of binary: /opt/conda/bin/python
2025-03-04 14:55:42：Traceback (most recent call last):
2025-03-04 14:55:42：  File "/opt/conda/bin/torchrun", line 8, in <module>
2025-03-04 14:55:42：    sys.exit(main())
2025-03-04 14:55:42：  File "/opt/conda/lib/python3.8/site-packages/torch/distributed/elastic/multiprocessing/errors/__init__.py", line 347, in wrapper
2025-03-04 14:55:42：    return f(*args, **kwargs)
2025-03-04 14:55:42：  File "/opt/conda/lib/python3.8/site-packages/torch/distributed/run.py", line 812, in main
2025-03-04 14:55:42：    run(args)
2025-03-04 14:55:42：  File "/opt/conda/lib/python3.8/site-packages/torch/distributed/run.py", line 803, in run
2025-03-04 14:55:42：    elastic_launch(
2025-0-04 14:55:42：  File "/opt/conda/lib/python3.8/site-packages/torch/distributed/launcher/api.py", line 135, in __call__
2025-03-04 14:55:42：    return launch_agent(self._config, self._entrypoint, list(args))
2025-03-04 14:55:42：  File "/opt/conda/lib/python3.8/site-packages/torch/distributed/launcher/api.py", line 268, in launch_agent
2025-03-04 14:55:42：    raise ChildFailedError(
2025-03-04 14:55:42：torch.distributed.elastic.multiprocessing.errors.ChildFailedError: 
2025-03-04 14:55:42：============================================================
2025-03-04 14:55:42：./finetuning.py FAILED
2025-03-04 14:55:42：------------------------------------------------------------
2025-03-04 14:55:42：Failures:
2025-03-04 14:55:42：  <NO_OTHER_FAILURES>
2025-03-04 14:55:42：------------------------------------------------------------
2025-03-04 14:55:42：Root Cause (first observed failure):
2025-03-04 14:55:42：[0]:
2025-03-04 14:55:42：  time      : 2025-03-04_14:55:42
2025-03-04 14:55:42：  host      : fullflow-9793-task2-0-19207-task1-0.fullflow-9793-task2-0-19207.bita-user.svc.cluster.local
2025-03-04 14:55:42：  rank      : 1 (local_rank: 0)
2025-03-04 14:55:42：  exitcode  : 1 (pid: 95)
2025-03-04 14:55:42：  error_file: <N/A>
2025-03-04 14:55:42：  traceback : To enable traceback see: https://pytorch.org/docs/stable/elastic/errors.html
2025-03-04 14:55:42：============================================================
2025-03-04 14:55:43：Do you wish to run the custom code? [y/N] 
```

**解决方案**： 针对错误的集群，重新同步挂载的训练脚本（llama-recipes）和数据集脚本（samsum）

使用的账号密码如下：

账号：<<EMAIL>>
密码：bita1234
