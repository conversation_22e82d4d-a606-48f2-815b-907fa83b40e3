
**常用操作**

- 清空文件      ：%d
- 搜索关键字   ：/关键字
- 删除当前行：   dd
- 显示行号    ：set nu
- 定位到文档第几行    ：n(行号)
- 0 移动到行头
- G 光标定位到最后一行的行首
- gg 光标定位到第一行的行首
- nG 光标定位到第 n 行的行首
- u 撤销刚才的操作
- d$删除光标到本行结尾

**命令操作**

- 设置文档的编码格式    ：set enc=utf8
- 取消行号    ：set nonu
- 删除多行(从n1行到n2行)文本 ：n1 ,n2d
- 在当前行进行文本替换 ：s/str1/str2/g       将str1替换成str2
- 在全文中进行文本替换 ：%s/str1/str2/g
- 在区域内进行替换：n1,n2s/str1/str2/g

**正常模式**

- H 光标定位到当前屏幕的第一行行首
- M 光标移动到当前屏幕的中间
- L 光标移动到当前屏幕的尾部
- zt 把当前行移动到当前屏幕的最上方，也就是第一行
- zz 把当前行移动到当前屏幕的中间
- zb 把当前行移动到当前屏幕的尾部
- % 匹配括号移动，包括 ( , { , [ 需要把光标先移动到括号上
- \* 和 # 匹配光标当前所在的单词，移动光标到下一个（或者上一个）匹配的单词（ * 是下一个，# 是上一个）

**删除操作**

- dw 删除一个单词
- dnw 删除 n 个单词
- ndd 删除光标处开始的 n 行
- dG 删除光标所在行到文本的结束

**复制操作**

- yw 复制一个单词，还有 ynw
- yfa 复制光标到下一个 a 的字符处,还有ynfa
- yy 复制一行，还有 nyy
- y$ 复制光标到本行的结尾
- yH 复制屏幕显示的第一行文本到光标所在的行
- yG 复制光标所在行到文本的结束
- p 在光标后开始复制
- ctrl + r 恢复撤销操作

**vim出现insert（visual）问题**

执行vim ~/.vimrc
编辑~/.vimrc往里添加并保存即可：
set clipboard=unnamed

**$'\r': command not found**

set ff=unix

**vim 复制粘帖出现错位、自动缩进 问题的解决办法**

1、先进入paste模式：
set paste
2、再按键 i 进入插入模式，然后再粘贴

**永久显示行号**

vim ~/.vimrc
在最后一行输入 ：set nu
