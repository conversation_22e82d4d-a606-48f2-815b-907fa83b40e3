# 4090 开启p2p通信性能测试

测试环境：4090，单机八卡
目的： 测试在单机多卡的情况下，通过shm和p2p进行训练推理的性能差异。

## 通信库算子性能检测

通过docker的方式挂载宿主机所以计算卡来进行通信库通信性能测试。

```bash
docker run --rm -it --net=host --shm-size=8G -v ./training_example/:/home/<USER>/aiccperf/nccl-tests:v2.13-nccl2.22-cuda12.6 bash
```

测试结果如下：

| 测试命令                                                     | 通信方式 | 带宽(G/s)                     |
| ------------------------------------------------------------ | -------- | ----------------------------- |
| NCCL_P2P_LEVEL=SYS ./all_reduce_perf -b 32M -e 8G  -f 2 -g 8 | p2p      | 17.7984,  17.7607,   17.7857  |
| ./all_reduce_perf -b 32M -e 8G  -f 2 -g 8                    | shm      | 15.6757,   15.4367,   15.3023 |

每个测试项执行三次，取平均结果进行计算，最后得出通过开启p2p通信的情况下执行all_reduce单机八卡通信检测，通信速率提升约**14.9%**。

### dinov2 使用imagenet 单个类别图像进行单机八卡训练性能检测

使用dinov2 官方代码进行训练性能测试。

具体验证流程如下：

```bash

docker run -it --rm --shm-size=8g --gpus=all -v ./train/:/home/<USER>/  chjkusters4/dinov2:V4  bash

# 编译dinov2

pip3 install -e .

## 通过imagenet 生成extra 元数据

from dinov2.data.datasets import ImageNet

for split in ImageNet.Split:
    dataset = ImageNet(split=split, root="/home/<USER>/imagenet", extra="/home/<USER>/extra")
    dataset.dump_extra()


## 通过torchrun的方式执行单机八卡测试

NCCL_DEBUG=INFO NCCL_P2P_LEVEL=SYS
torchrun --nproc_per_node=8 \
         --nnodes=1 \
         --master_addr="127.0.0.1" \
         --master_port=29500 \
         dinov2/train/train.py \
         --config-file dinov2/configs/train/vitl16_short.yaml \
         --output-dir /home/<USER>/output \
          train.dataset_path=ImageNet:split=TRAIN:root=/home/<USER>/imagenet:extra=/home/<USER>/extra
```

测试结果如下：

| 模型参数量 | 通信方式 | 总训练花费时间（m）        |
| ---------- | -------- | -------------------------- |
| 625M       | p2p      | 0:06:53, 0:06:54, 0:06:53  |
| 625M       | shm      | 0:06:58,  0:07:00, 0:07:00 |
| 1.3G       | p2p      | 0:07:02,  0:07:03, 0:07:04 |
| 1.3G       | shm      | 0:07:25,  0:07:23, 0:07:31 |

上表中，不同参数量和通信方式每个都做了三次训练并获取结果，按照总的训练花费时间来进行对比，**在模型参数1.3G的情况下，开启p2p通信能够提高大约5%的训练速度。在模型参数625M的情况下，开启p2p通信，对模型训练的速率提升没有明显效果。**

### QwQ32B 单机八卡推理性能检测

实验流程如下：

（1）通过vllm 部署QwQ32B 服务。

```bash
docker run -d --name qwq \
    --network host \
    --entrypoint "" \
    --privileged \
    --shm-size=10.24gb \
    -e NCCL_DEBUG=INFO \
    -e NCCL_P2P_LEVEL=SYS \   # 开启p2p通信
    -e NCCL_DEBUG_FILE=nccl_log_%h_%p \
    -v /home/<USER>/wyq/QwQ-32B/:/home/<USER>/ \
    registry.cnbita.com:5000/wangshi/vllm-openai:v0.8.1 \
   vllm serve /home/<USER>
```

(2) 查看服务是否启动成功，接口请求是否正常

```bash
curl  http://localhost:8001/v1/models

curl -X POST http://localhost:8001/v1/chat/completions  -H "Content-Type: application/json"  -d '{ "model": "QwQ-32B", "messages": [ {"role": "system", "content": "You are a helpful assistant."}, {"role": "user", "content": "用 4、1、9 组成的三位数造减法塔，最后一层的算式是什么?"} ] }' 
```

(3) 启动vllm-benchmark 服务

```bash
git clone https://github.com/vllm-project/vllm.git

docker run -it --name qwq-benchmark \
    --network host \
    --entrypoint "" \
    --privileged \
    --shm-size=10.24gb \
    -e NCCL_DEBUG=INFO \
    -e NCCL_DEBUG_FILE=nccl_log_%h_%p \
    -v /home/<USER>/wyq/QwQ-32B/:/home/<USER>/ \
    -v /home/<USER>/wyq/vllm/benchmarks:/home/<USER>
    registry.cnbita.com:5000/wangshi/vllm-openai:v0.8.1 \
   bash

```

（4） 执行benchmark 命令进行性能测试对比

```bash
python3 benchmark_serving.py  --backend vllm  --model /home/<USER>
```

测试结果如下：

| 并发度 | 通信方式 | Request throughput(req/s) | Output token throughput(tok/s) | Total Token throughput(tok/s) |
| ------ | -------- | ------------------------- | ------------------------------ | ----------------------------- |
| 16     | p2p      | 0.75 (**10.29%**)         | 706.83 (**10.48%**)            | 1477.73 (**10.38%**)          |
| 16     | shm      | 0.68                      | 639.46                         | 1338.66                       |
| 32     | p2p      | 1.02 (**8.51%**)          | 949.58 (**6.98%**)             | 1992.03 (**7.38%**)           |
| 32     | shm      | 0.94                      | 887.97                         | 1855.57                       |
| 64     | p2p      | 1.39 (**12.09%**)         | 1323.91 (**14.74%**)           | 2750.50 (**13.26%**)          |
| 64     | shm      | 1.24                      | 1153.95                        | 2428.00                       |

测试结果表明，开启p2p通信能够提高推理的性能，其中并发度为16的时候，推理性能大约提升10%左右；并发度为32的时候，推理性能提升7.5%左右；并发度为64的时候，推理性能提升13%左右。
